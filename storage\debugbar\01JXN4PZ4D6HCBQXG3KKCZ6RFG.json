{"__meta": {"id": "01JXN4PZ4D6HCBQXG3KKCZ6RFG", "datetime": "2025-06-13 17:17:01", "utime": **********.454399, "method": "POST", "uri": "/admin/marketplaces/stores/import/import", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749834984.79392, "end": **********.454425, "duration": 36.660505056381226, "duration_str": "36.66s", "measures": [{"label": "Booting", "start": 1749834984.79392, "relative_start": 0, "end": **********.535338, "relative_end": **********.535338, "duration": 0.****************, "duration_str": "741ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.535367, "relative_start": 0.****************, "end": **********.454429, "relative_end": 3.814697265625e-06, "duration": 35.**************, "duration_str": "35.92s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.556895, "relative_start": 0.****************, "end": **********.570994, "relative_end": **********.570994, "duration": 0.014098882675170898, "duration_str": "14.1ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.448792, "relative_start": 36.**************, "end": **********.450508, "relative_end": **********.450508, "duration": 0.0017161369323730469, "duration_str": "1.72ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "52MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.3.17", "Environment": "localhost", "Debug Mode": "Enabled", "URL": "muhrak.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 500, "nb_statements": 61424, "nb_visible_statements": 500, "nb_excluded_statements": 60924, "nb_failed_statements": 0, "accumulated_duration": 0.30630000000000085, "accumulated_duration_str": "306ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft and hard limit for Debugbar are reached. Only the first 100 queries show details. Queries after the first 500 are ignored. Limits can be raised in the config (debugbar.options.db.soft/hard_limit).", "type": "info"}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}], "start": **********.5876381, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0, "width_percent": 0.167}, {"sql": "select `lang_locale`, `lang_code`, `lang_name`, `lang_flag`, `lang_is_rtl` from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\Providers\\HookServiceProvider.php", "line": 105}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "platform/core/base/src/Http/Middleware/AdminLocaleMiddleware.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php", "line": 28}], "start": **********.594352, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0.167, "width_percent": 0.118}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.120721, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0.284, "width_percent": 0.163}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.1238608, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 0.447, "width_percent": 0.362}, {"sql": "insert into `mp_stores` (`name`, `email`, `phone`, `address`, `country`, `state`, `city`, `zip_code`, `company`, `tax_id`, `description`, `content`, `logo`, `logo_square`, `cover_image`, `certificate_file`, `government_id_file`, `customer_id`, `status`, `updated_at`, `created_at`) values ('VOLVO', '<EMAIL>', '', '', '', '', '', '', '', '', '', '', 'v.png', null, null, null, null, 1, 'pending', '2025-06-13 17:16:26', '2025-06-13 17:16:26')", "type": "query", "params": [], "bindings": ["VOLVO", "<EMAIL>", "", "", "", "", "", "", "", "", "", "", "v.png", null, null, null, null, 1, {"value": "pending", "label": "Pending"}, "2025-06-13 17:16:26", "2025-06-13 17:16:26"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 302}, {"index": 16, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 18, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.129706, "duration": 0.00373, "duration_str": "3.73ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:302", "source": {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 302}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=302", "ajax": false, "filename": "StoreImporter.php", "line": "302"}, "connection": "muhrak", "explain": null, "start_percent": 0.81, "width_percent": 1.218}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.137814, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 2.027, "width_percent": 0.147}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.1401608, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 2.174, "width_percent": 0.196}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.1') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.1"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.141778, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 2.37, "width_percent": 0.124}, {"sql": "insert into `mp_stores` (`name`, `email`, `phone`, `address`, `country`, `state`, `city`, `zip_code`, `company`, `tax_id`, `description`, `content`, `logo`, `logo_square`, `cover_image`, `certificate_file`, `government_id_file`, `customer_id`, `status`, `updated_at`, `created_at`) values ('Komatsu', '<EMAIL>.1', '', '', '', '', '', '', '', '', '', '', 'k.png', null, null, null, null, 1, 'pending', '2025-06-13 17:16:26', '2025-06-13 17:16:26')", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON>", "<EMAIL>.1", "", "", "", "", "", "", "", "", "", "", "k.png", null, null, null, null, 1, {"value": "pending", "label": "Pending"}, "2025-06-13 17:16:26", "2025-06-13 17:16:26"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 302}, {"index": 16, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 18, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.146354, "duration": 0.004059999999999999, "duration_str": "4.06ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:302", "source": {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 302}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=302", "ajax": false, "filename": "StoreImporter.php", "line": "302"}, "connection": "muhrak", "explain": null, "start_percent": 2.494, "width_percent": 1.325}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.151788, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 3.82, "width_percent": 0.124}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.153577, "duration": 0.00383, "duration_str": "3.83ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 3.944, "width_percent": 1.25}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.1') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.1"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.158545, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 5.194, "width_percent": 0.124}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.2') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.2"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.1598608, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 5.318, "width_percent": 0.134}, {"sql": "insert into `mp_stores` (`name`, `email`, `phone`, `address`, `country`, `state`, `city`, `zip_code`, `company`, `tax_id`, `description`, `content`, `logo`, `logo_square`, `cover_image`, `certificate_file`, `government_id_file`, `customer_id`, `status`, `updated_at`, `created_at`) values ('HYUNDAI', '<EMAIL>.2', '', '', '', '', '', '', '', '', '', '', 'hy.png', null, null, null, null, 1, 'pending', '2025-06-13 17:16:26', '2025-06-13 17:16:26')", "type": "query", "params": [], "bindings": ["HYUNDAI", "<EMAIL>.2", "", "", "", "", "", "", "", "", "", "", "hy.png", null, null, null, null, 1, {"value": "pending", "label": "Pending"}, "2025-06-13 17:16:26", "2025-06-13 17:16:26"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 302}, {"index": 16, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 18, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.16488, "duration": 0.00382, "duration_str": "3.82ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:302", "source": {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 302}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=302", "ajax": false, "filename": "StoreImporter.php", "line": "302"}, "connection": "muhrak", "explain": null, "start_percent": 5.452, "width_percent": 1.247}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.1702468, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 6.699, "width_percent": 0.193}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.172107, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 6.892, "width_percent": 0.264}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.1') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.1"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.173981, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 7.156, "width_percent": 0.14}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.2') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.2"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.175291, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 7.297, "width_percent": 0.124}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.3') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.3"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.1765919, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 7.421, "width_percent": 0.47}, {"sql": "insert into `mp_stores` (`name`, `email`, `phone`, `address`, `country`, `state`, `city`, `zip_code`, `company`, `tax_id`, `description`, `content`, `logo`, `logo_square`, `cover_image`, `certificate_file`, `government_id_file`, `customer_id`, `status`, `updated_at`, `created_at`) values ('Caterpillar', '<EMAIL>.3', '', '', '', '', '', '', '', '', '', '', 'c.png', null, null, null, null, 1, 'pending', '2025-06-13 17:16:26', '2025-06-13 17:16:26')", "type": "query", "params": [], "bindings": ["Caterpillar", "<EMAIL>.3", "", "", "", "", "", "", "", "", "", "", "c.png", null, null, null, null, 1, {"value": "pending", "label": "Pending"}, "2025-06-13 17:16:26", "2025-06-13 17:16:26"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 302}, {"index": 16, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 18, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.183282, "duration": 0.0039, "duration_str": "3.9ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:302", "source": {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 302}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=302", "ajax": false, "filename": "StoreImporter.php", "line": "302"}, "connection": "muhrak", "explain": null, "start_percent": 7.891, "width_percent": 1.273}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.188694, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 9.164, "width_percent": 0.225}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.1907349, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 9.389, "width_percent": 0.137}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.1') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.1"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.192046, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 9.527, "width_percent": 0.127}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.2') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.2"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.193322, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 9.654, "width_percent": 0.134}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.3') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.3"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.194576, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 9.788, "width_percent": 0.124}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.4') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.4"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.195804, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 9.912, "width_percent": 0.15}, {"sql": "insert into `mp_stores` (`name`, `email`, `phone`, `address`, `country`, `state`, `city`, `zip_code`, `company`, `tax_id`, `description`, `content`, `logo`, `logo_square`, `cover_image`, `certificate_file`, `government_id_file`, `customer_id`, `status`, `updated_at`, `created_at`) values ('DOOSAN', '<EMAIL>.4', '', '', '', '', '', '', '', '', '', '', 'doosan-logo.png', null, null, null, null, 1, 'pending', '2025-06-13 17:16:26', '2025-06-13 17:16:26')", "type": "query", "params": [], "bindings": ["DOOSAN", "<EMAIL>.4", "", "", "", "", "", "", "", "", "", "", "doosan-logo.png", null, null, null, null, 1, {"value": "pending", "label": "Pending"}, "2025-06-13 17:16:26", "2025-06-13 17:16:26"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 302}, {"index": 16, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 18, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.201176, "duration": 0.00419, "duration_str": "4.19ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:302", "source": {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 302}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=302", "ajax": false, "filename": "StoreImporter.php", "line": "302"}, "connection": "muhrak", "explain": null, "start_percent": 10.062, "width_percent": 1.368}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.20681, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 11.43, "width_percent": 0.131}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.208328, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 11.561, "width_percent": 0.16}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.1') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.1"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.209698, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 11.721, "width_percent": 0.124}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.2') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.2"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.210926, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 11.845, "width_percent": 0.144}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.3') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.3"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.212225, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 11.988, "width_percent": 0.127}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.4') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.4"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.213716, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 12.116, "width_percent": 0.153}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.5') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.5"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.215215, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 12.269, "width_percent": 0.167}, {"sql": "insert into `mp_stores` (`name`, `email`, `phone`, `address`, `country`, `state`, `city`, `zip_code`, `company`, `tax_id`, `description`, `content`, `logo`, `logo_square`, `cover_image`, `certificate_file`, `government_id_file`, `customer_id`, `status`, `updated_at`, `created_at`) values ('Mercedes', '<EMAIL>.5', '', '', '', '', '', '', '', '', '', '', 'm.png', null, null, null, null, 1, 'pending', '2025-06-13 17:16:26', '2025-06-13 17:16:26')", "type": "query", "params": [], "bindings": ["Mercedes", "<EMAIL>.5", "", "", "", "", "", "", "", "", "", "", "m.png", null, null, null, null, 1, {"value": "pending", "label": "Pending"}, "2025-06-13 17:16:26", "2025-06-13 17:16:26"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 302}, {"index": 16, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 18, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.220761, "duration": 0.004070000000000001, "duration_str": "4.07ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:302", "source": {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 302}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=302", "ajax": false, "filename": "StoreImporter.php", "line": "302"}, "connection": "muhrak", "explain": null, "start_percent": 12.436, "width_percent": 1.329}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.226282, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 13.764, "width_percent": 0.127}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.22781, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 13.892, "width_percent": 0.14}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.1') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.1"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.229202, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 14.032, "width_percent": 0.15}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.2') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.2"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.230575, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 14.182, "width_percent": 0.118}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.3') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.3"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.2319589, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 14.3, "width_percent": 0.131}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.4') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.4"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.233444, "duration": 0.00177, "duration_str": "1.77ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 14.43, "width_percent": 0.578}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.5') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.5"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.236418, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 15.008, "width_percent": 0.167}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.6') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.6"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.2378762, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 15.175, "width_percent": 0.206}, {"sql": "insert into `mp_stores` (`name`, `email`, `phone`, `address`, `country`, `state`, `city`, `zip_code`, `company`, `tax_id`, `description`, `content`, `logo`, `logo_square`, `cover_image`, `certificate_file`, `government_id_file`, `customer_id`, `status`, `updated_at`, `created_at`) values ('Scania', '<EMAIL>.6', '', '', '', '', '', '', '', '', '', '<p>scania details</p>', 's.png', 'dhims.jpg', null, null, null, 1, 'pending', '2025-06-13 17:16:26', '2025-06-13 17:16:26')", "type": "query", "params": [], "bindings": ["Scania", "<EMAIL>.6", "", "", "", "", "", "", "", "", "", "<p>scania details</p>", "s.png", "dhims.jpg", null, null, null, 1, {"value": "pending", "label": "Pending"}, "2025-06-13 17:16:26", "2025-06-13 17:16:26"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 302}, {"index": 16, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 18, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.243341, "duration": 0.00404, "duration_str": "4.04ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:302", "source": {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 302}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=302", "ajax": false, "filename": "StoreImporter.php", "line": "302"}, "connection": "muhrak", "explain": null, "start_percent": 15.38, "width_percent": 1.319}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.248977, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 16.699, "width_percent": 0.14}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.25071, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 16.84, "width_percent": 0.167}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.1') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.1"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.25238, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 17.006, "width_percent": 0.193}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.2') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.2"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.253933, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 17.199, "width_percent": 0.16}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.3') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.3"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.255467, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 17.359, "width_percent": 0.137}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.4') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.4"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.256737, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 17.496, "width_percent": 0.153}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.5') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.5"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.25812, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 17.649, "width_percent": 0.127}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.6') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.6"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.2594302, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 17.777, "width_percent": 0.15}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.7') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.7"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.260957, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 17.927, "width_percent": 0.157}, {"sql": "insert into `mp_stores` (`name`, `email`, `phone`, `address`, `country`, `state`, `city`, `zip_code`, `company`, `tax_id`, `description`, `content`, `logo`, `logo_square`, `cover_image`, `certificate_file`, `government_id_file`, `customer_id`, `status`, `updated_at`, `created_at`) values ('JCB', '<EMAIL>.7', '', '', '', '', '', '', '', '', '', '', 'jcb.png', null, null, null, null, 1, 'pending', '2025-06-13 17:16:26', '2025-06-13 17:16:26')", "type": "query", "params": [], "bindings": ["JCB", "<EMAIL>.7", "", "", "", "", "", "", "", "", "", "", "jcb.png", null, null, null, null, 1, {"value": "pending", "label": "Pending"}, "2025-06-13 17:16:26", "2025-06-13 17:16:26"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 302}, {"index": 16, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 18, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.266383, "duration": 0.0038399999999999997, "duration_str": "3.84ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:302", "source": {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 302}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=302", "ajax": false, "filename": "StoreImporter.php", "line": "302"}, "connection": "muhrak", "explain": null, "start_percent": 18.084, "width_percent": 1.254}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.2716649, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 19.337, "width_percent": 0.196}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.27357, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 19.533, "width_percent": 0.163}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.1') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.1"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.275054, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 19.696, "width_percent": 0.14}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.2') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.2"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.276944, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 19.837, "width_percent": 0.167}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.3') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.3"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.279566, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 20.003, "width_percent": 0.173}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.4') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.4"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.281587, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 20.176, "width_percent": 0.173}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.5') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.5"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.283489, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 20.349, "width_percent": 0.245}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.6') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.6"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.285757, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 20.594, "width_percent": 0.284}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.7') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.7"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.287918, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 20.878, "width_percent": 0.222}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.8') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.8"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.290107, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 21.1, "width_percent": 0.209}, {"sql": "insert into `mp_stores` (`name`, `email`, `phone`, `address`, `country`, `state`, `city`, `zip_code`, `company`, `tax_id`, `description`, `content`, `logo`, `logo_square`, `cover_image`, `certificate_file`, `government_id_file`, `customer_id`, `status`, `updated_at`, `created_at`) values ('MAN', '<EMAIL>.8', '', '', '', '', '', '', '', '', '', '', 'man.png', null, null, null, null, 1, 'pending', '2025-06-13 17:16:26', '2025-06-13 17:16:26')", "type": "query", "params": [], "bindings": ["MAN", "<EMAIL>.8", "", "", "", "", "", "", "", "", "", "", "man.png", null, null, null, null, 1, {"value": "pending", "label": "Pending"}, "2025-06-13 17:16:26", "2025-06-13 17:16:26"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 302}, {"index": 16, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 18, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.2954638, "duration": 0.00376, "duration_str": "3.76ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:302", "source": {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 302}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=302", "ajax": false, "filename": "StoreImporter.php", "line": "302"}, "connection": "muhrak", "explain": null, "start_percent": 21.309, "width_percent": 1.228}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.301759, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 22.537, "width_percent": 0.153}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.303991, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 22.69, "width_percent": 0.18}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.1') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.1"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.305891, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 22.87, "width_percent": 0.209}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.2') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.2"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.307559, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 23.079, "width_percent": 0.118}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.3') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.3"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.309047, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 23.196, "width_percent": 0.095}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.4') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.4"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.310301, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 23.291, "width_percent": 0.111}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.5') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.5"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.3115551, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 23.402, "width_percent": 0.108}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.6') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.6"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.312898, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 23.51, "width_percent": 0.108}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.7') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.7"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.314323, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 23.617, "width_percent": 0.183}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.8') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.8"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.316199, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 23.8, "width_percent": 0.173}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.9') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.9"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.3183079, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 23.973, "width_percent": 0.196}, {"sql": "insert into `mp_stores` (`name`, `email`, `phone`, `address`, `country`, `state`, `city`, `zip_code`, `company`, `tax_id`, `description`, `content`, `logo`, `logo_square`, `cover_image`, `certificate_file`, `government_id_file`, `customer_id`, `status`, `updated_at`, `created_at`) values ('TATA', '<EMAIL>.9', '', '', '', '', '', '', '', '', '', '', 'tata.png', null, null, null, null, 1, 'pending', '2025-06-13 17:16:26', '2025-06-13 17:16:26')", "type": "query", "params": [], "bindings": ["TATA", "<EMAIL>.9", "", "", "", "", "", "", "", "", "", "", "tata.png", null, null, null, null, 1, {"value": "pending", "label": "Pending"}, "2025-06-13 17:16:26", "2025-06-13 17:16:26"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 302}, {"index": 16, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 18, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.325824, "duration": 0.004059999999999999, "duration_str": "4.06ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:302", "source": {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 302}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=302", "ajax": false, "filename": "StoreImporter.php", "line": "302"}, "connection": "muhrak", "explain": null, "start_percent": 24.169, "width_percent": 1.325}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.331421, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 25.495, "width_percent": 0.137}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.3332782, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 25.632, "width_percent": 0.153}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.1') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.1"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.335868, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 25.785, "width_percent": 0.287}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.2') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.2"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.338088, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 26.072, "width_percent": 0.176}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.3') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.3"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.339681, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 26.249, "width_percent": 0.131}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.4') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.4"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.341116, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 26.379, "width_percent": 0.101}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.5') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.5"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.3422759, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 26.481, "width_percent": 0.124}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.6') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.6"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.34353, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 26.605, "width_percent": 0.104}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.7') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.7"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.344725, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 26.709, "width_percent": 0.124}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.8') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.8"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.346056, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 26.833, "width_percent": 0.121}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.9') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.9"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.347406, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 26.954, "width_percent": 0.14}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.10') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.10"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.3488681, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 27.094, "width_percent": 0.153}, {"sql": "insert into `mp_stores` (`name`, `email`, `phone`, `address`, `country`, `state`, `city`, `zip_code`, `company`, `tax_id`, `description`, `content`, `logo`, `logo_square`, `cover_image`, `certificate_file`, `government_id_file`, `customer_id`, `status`, `updated_at`, `created_at`) values ('Daewoo', '<EMAIL>.10', '', '', '', '', '', '', '', '', '', '', 'daewoo.png', null, null, null, null, 1, 'pending', '2025-06-13 17:16:26', '2025-06-13 17:16:26')", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON>", "<EMAIL>.10", "", "", "", "", "", "", "", "", "", "", "daewoo.png", null, null, null, null, 1, {"value": "pending", "label": "Pending"}, "2025-06-13 17:16:26", "2025-06-13 17:16:26"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 302}, {"index": 16, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 17, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 18, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.354302, "duration": 0.00377, "duration_str": "3.77ms", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:302", "source": {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 302}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=302", "ajax": false, "filename": "StoreImporter.php", "line": "302"}, "connection": "muhrak", "explain": null, "start_percent": 27.248, "width_percent": 1.231}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 253}, {"index": 21, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 22, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}], "start": **********.359487, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 28.479, "width_percent": 0.101}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.360981, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 28.58, "width_percent": 0.137}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.1') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.1"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.362333, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 28.717, "width_percent": 0.108}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.2') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.2"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.3635461, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 28.825, "width_percent": 0.101}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.3') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.3"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.364814, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 28.926, "width_percent": 0.127}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.4') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.4"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.366307, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 29.053, "width_percent": 0.261}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.5') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.5"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.3681269, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 29.314, "width_percent": 0.14}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.6') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.6"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.369679, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 29.455, "width_percent": 0.215}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.7') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.7"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.3714352, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 29.67, "width_percent": 0.147}, {"sql": "select exists(select * from `mp_stores` where `email` = '<EMAIL>.8') as `exists`", "type": "query", "params": [], "bindings": ["<EMAIL>.8"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 229}, {"index": 13, "namespace": null, "name": "vendor/botble/data-synchronize/src/Importer/Importer.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Importer\\Importer.php", "line": 208}, {"index": 14, "namespace": null, "name": "vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Http\\Controllers\\ImportController.php", "line": 80}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.37292, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "StoreImporter.php:273", "source": {"index": 11, "namespace": null, "name": "platform/plugins/marketplace/src/Importers/StoreImporter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Importers\\StoreImporter.php", "line": 273}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FImporters%2FStoreImporter.php&line=273", "ajax": false, "filename": "StoreImporter.php", "line": "273"}, "connection": "muhrak", "explain": null, "start_percent": 29.817, "width_percent": 0.147}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.374279, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 29.964, "width_percent": 0.111}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.374736, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 30.075, "width_percent": 0.111}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.375192, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 30.186, "width_percent": 0.134}, {"sql": "insert into `mp_stores` (`name`, `email`, `phone`, `address`, `country`, `state`, `city`, `zip_code`, `company`, `tax_id`, `description`, `content`, `logo`, `logo_square`, `cover_image`, `certificate_file`, `government_id_file`, `customer_id`, `status`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.379641, "duration": 0.00368, "duration_str": "3.68ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 30.32, "width_percent": 1.201}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.384335, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 31.521, "width_percent": 0.114}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.385238, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 31.636, "width_percent": 0.14}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.385931, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 31.776, "width_percent": 0.225}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.386821, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 32.001, "width_percent": 0.118}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.38732, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 32.119, "width_percent": 0.131}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.387869, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 32.249, "width_percent": 0.137}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.388434, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 32.387, "width_percent": 0.118}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.388939, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 32.504, "width_percent": 0.199}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.389688, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 32.703, "width_percent": 0.153}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3902931, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 32.857, "width_percent": 0.104}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.39074, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 32.961, "width_percent": 0.124}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.391261, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 33.085, "width_percent": 0.091}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.391672, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 33.177, "width_percent": 0.121}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.392184, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 33.297, "width_percent": 0.121}, {"sql": "insert into `mp_stores` (`name`, `email`, `phone`, `address`, `country`, `state`, `city`, `zip_code`, `company`, `tax_id`, `description`, `content`, `logo`, `logo_square`, `cover_image`, `certificate_file`, `government_id_file`, `customer_id`, `status`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3969212, "duration": 0.00382, "duration_str": "3.82ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 33.418, "width_percent": 1.247}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.401252, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 34.665, "width_percent": 0.111}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.402211, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 34.776, "width_percent": 0.137}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.402854, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 34.913, "width_percent": 0.14}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.403492, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 35.054, "width_percent": 0.127}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.404059, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 35.181, "width_percent": 0.14}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.404666, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 35.322, "width_percent": 0.157}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.405318, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 35.478, "width_percent": 0.144}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.406005, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 35.622, "width_percent": 0.134}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.406568, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 35.756, "width_percent": 0.121}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.407081, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 35.877, "width_percent": 0.124}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.407588, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 36.001, "width_percent": 0.118}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.408102, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 36.118, "width_percent": 0.121}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.408675, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 36.239, "width_percent": 0.127}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.40926, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 36.366, "width_percent": 0.127}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.40977, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 36.494, "width_percent": 0.137}, {"sql": "insert into `mp_stores` (`name`, `email`, `phone`, `address`, `country`, `state`, `city`, `zip_code`, `company`, `tax_id`, `description`, `content`, `logo`, `logo_square`, `cover_image`, `certificate_file`, `government_id_file`, `customer_id`, `status`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.414027, "duration": 0.00464, "duration_str": "4.64ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 36.631, "width_percent": 1.515}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.419416, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 38.146, "width_percent": 0.199}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.420708, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 38.345, "width_percent": 0.189}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.421449, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 38.534, "width_percent": 0.121}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.421949, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 38.655, "width_percent": 0.137}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4225588, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 38.792, "width_percent": 0.14}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.423132, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 38.932, "width_percent": 0.124}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.423641, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 39.056, "width_percent": 0.114}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.424161, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 39.171, "width_percent": 0.111}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.424639, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 39.282, "width_percent": 0.108}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.42511, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 39.389, "width_percent": 0.104}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.42556, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 39.494, "width_percent": 0.098}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.425986, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 39.592, "width_percent": 0.111}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.426451, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 39.703, "width_percent": 0.108}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.426915, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 39.811, "width_percent": 0.114}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4274, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 39.925, "width_percent": 0.111}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.427885, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 40.036, "width_percent": 0.111}, {"sql": "insert into `mp_stores` (`name`, `email`, `phone`, `address`, `country`, `state`, `city`, `zip_code`, `company`, `tax_id`, `description`, `content`, `logo`, `logo_square`, `cover_image`, `certificate_file`, `government_id_file`, `customer_id`, `status`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.432825, "duration": 0.00398, "duration_str": "3.98ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 40.147, "width_percent": 1.299}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.437434, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 41.446, "width_percent": 0.167}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.438542, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 41.613, "width_percent": 0.186}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4392772, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 41.799, "width_percent": 0.153}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4399018, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 41.952, "width_percent": 0.137}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4404578, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 42.089, "width_percent": 0.131}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.441012, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 42.22, "width_percent": 0.114}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.441507, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 42.334, "width_percent": 0.108}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.441968, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 42.442, "width_percent": 0.124}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.442486, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 42.566, "width_percent": 0.121}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.442981, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 42.687, "width_percent": 0.121}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.443489, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 42.808, "width_percent": 0.118}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.443989, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 42.925, "width_percent": 0.121}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.444527, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 43.046, "width_percent": 0.118}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.445072, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 43.164, "width_percent": 0.124}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.445603, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 43.288, "width_percent": 0.114}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.446089, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 43.402, "width_percent": 0.111}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.446543, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 43.513, "width_percent": 0.114}, {"sql": "insert into `mp_stores` (`name`, `email`, `phone`, `address`, `country`, `state`, `city`, `zip_code`, `company`, `tax_id`, `description`, `content`, `logo`, `logo_square`, `cover_image`, `certificate_file`, `government_id_file`, `customer_id`, `status`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.452698, "duration": 0.0046, "duration_str": "4.6ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 43.627, "width_percent": 1.502}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4579349, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 45.129, "width_percent": 0.131}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.458851, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 45.26, "width_percent": 0.144}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.459411, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 45.403, "width_percent": 0.124}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4599152, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 45.527, "width_percent": 0.144}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.460487, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 45.671, "width_percent": 0.124}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.460999, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 45.795, "width_percent": 0.127}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.461522, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 45.922, "width_percent": 0.111}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.461987, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 46.033, "width_percent": 0.127}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.462502, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 46.161, "width_percent": 0.111}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4629781, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 46.272, "width_percent": 0.111}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.463483, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 46.383, "width_percent": 0.114}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.464005, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 46.497, "width_percent": 0.121}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.464541, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 46.618, "width_percent": 0.127}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.465066, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 46.745, "width_percent": 0.131}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.465608, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 46.876, "width_percent": 0.131}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.466153, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 47.006, "width_percent": 0.124}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.466731, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 47.13, "width_percent": 0.118}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.467288, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 47.248, "width_percent": 0.124}, {"sql": "insert into `mp_stores` (`name`, `email`, `phone`, `address`, `country`, `state`, `city`, `zip_code`, `company`, `tax_id`, `description`, `content`, `logo`, `logo_square`, `cover_image`, `certificate_file`, `government_id_file`, `customer_id`, `status`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.476114, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 47.372, "width_percent": 1.26}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4807072, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 48.632, "width_percent": 0.16}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.481751, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 48.792, "width_percent": 0.157}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4824288, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 48.949, "width_percent": 0.14}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.483091, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 49.089, "width_percent": 0.153}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4837449, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 49.243, "width_percent": 0.127}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.484273, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 49.37, "width_percent": 0.121}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.484782, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 49.491, "width_percent": 0.121}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.485297, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 49.611, "width_percent": 0.121}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4858682, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 49.732, "width_percent": 0.144}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.486556, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 49.876, "width_percent": 0.153}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.487226, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 50.029, "width_percent": 0.134}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.48783, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 50.163, "width_percent": 0.127}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.488349, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 50.291, "width_percent": 0.111}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4888132, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 50.402, "width_percent": 0.153}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.489453, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 50.555, "width_percent": 0.111}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.489929, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 50.666, "width_percent": 0.104}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.490365, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 50.77, "width_percent": 0.108}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4908059, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 50.878, "width_percent": 0.127}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.491304, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.006, "width_percent": 0.118}, {"sql": "insert into `mp_stores` (`name`, `email`, `phone`, `address`, `country`, `state`, `city`, `zip_code`, `company`, `tax_id`, `description`, `content`, `logo`, `logo_square`, `cover_image`, `certificate_file`, `government_id_file`, `customer_id`, `status`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.495494, "duration": 0.00387, "duration_str": "3.87ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.123, "width_percent": 1.263}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.500402, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 52.387, "width_percent": 0.15}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5018442, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 52.537, "width_percent": 0.16}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.502656, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 52.697, "width_percent": 0.193}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5035288, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 52.889, "width_percent": 0.127}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.504209, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 53.017, "width_percent": 0.144}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5048459, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 53.16, "width_percent": 0.121}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.505345, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 53.281, "width_percent": 0.118}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.505843, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 53.399, "width_percent": 0.114}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.506329, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 53.513, "width_percent": 0.114}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.506847, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 53.627, "width_percent": 0.121}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.507384, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 53.748, "width_percent": 0.147}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5079658, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 53.895, "width_percent": 0.134}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.508503, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.029, "width_percent": 0.104}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.508965, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.133, "width_percent": 0.111}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.509439, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.244, "width_percent": 0.127}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.509956, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.372, "width_percent": 0.124}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.510464, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.496, "width_percent": 0.124}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5109649, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.62, "width_percent": 0.121}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.511464, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.74, "width_percent": 0.114}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.511955, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.855, "width_percent": 0.118}, {"sql": "insert into `mp_stores` (`name`, `email`, `phone`, `address`, `country`, `state`, `city`, `zip_code`, `company`, `tax_id`, `description`, `content`, `logo`, `logo_square`, `cover_image`, `certificate_file`, `government_id_file`, `customer_id`, `status`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.51713, "duration": 0.00385, "duration_str": "3.85ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.972, "width_percent": 1.257}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5214832, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 56.229, "width_percent": 0.144}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.52248, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 56.373, "width_percent": 0.16}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5231209, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 56.533, "width_percent": 0.137}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5236828, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 56.67, "width_percent": 0.131}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.524219, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 56.801, "width_percent": 0.131}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.524759, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 56.931, "width_percent": 0.118}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.525259, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 57.049, "width_percent": 0.114}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.525746, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 57.163, "width_percent": 0.114}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.526234, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 57.277, "width_percent": 0.114}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.526722, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 57.391, "width_percent": 0.114}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.52721, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 57.506, "width_percent": 0.101}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.527652, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 57.607, "width_percent": 0.095}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.528063, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 57.702, "width_percent": 0.095}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.52847, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 57.796, "width_percent": 0.095}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.528875, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 57.891, "width_percent": 0.095}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.529279, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 57.986, "width_percent": 0.095}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.529685, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 58.08, "width_percent": 0.095}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5300882, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 58.175, "width_percent": 0.095}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.530495, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 58.27, "width_percent": 0.104}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.530975, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 58.374, "width_percent": 0.147}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5316231, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 58.521, "width_percent": 0.144}, {"sql": "insert into `mp_stores` (`name`, `email`, `phone`, `address`, `country`, `state`, `city`, `zip_code`, `company`, `tax_id`, `description`, `content`, `logo`, `logo_square`, `cover_image`, `certificate_file`, `government_id_file`, `customer_id`, `status`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5359411, "duration": 0.0039900000000000005, "duration_str": "3.99ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 58.665, "width_percent": 1.303}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.540484, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 59.967, "width_percent": 0.14}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5414689, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 60.108, "width_percent": 0.137}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.542021, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 60.245, "width_percent": 0.124}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5425348, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 60.369, "width_percent": 0.131}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.543067, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 60.5, "width_percent": 0.124}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.543586, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 60.624, "width_percent": 0.137}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.544143, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 60.761, "width_percent": 0.131}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.544719, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 60.891, "width_percent": 0.127}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.545245, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 61.019, "width_percent": 0.108}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.545713, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 61.126, "width_percent": 0.131}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.546254, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 61.257, "width_percent": 0.118}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.546755, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 61.374, "width_percent": 0.114}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.547247, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 61.489, "width_percent": 0.104}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.547702, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 61.593, "width_percent": 0.114}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.548178, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 61.707, "width_percent": 0.101}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.548615, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 61.809, "width_percent": 0.104}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.549076, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 61.913, "width_percent": 0.114}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.549561, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 62.027, "width_percent": 0.111}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.550036, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 62.138, "width_percent": 0.118}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.550522, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 62.256, "width_percent": 0.098}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.550939, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 62.354, "width_percent": 0.104}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.551382, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 62.458, "width_percent": 0.108}, {"sql": "insert into `mp_stores` (`name`, `email`, `phone`, `address`, `country`, `state`, `city`, `zip_code`, `company`, `tax_id`, `description`, `content`, `logo`, `logo_square`, `cover_image`, `certificate_file`, `government_id_file`, `customer_id`, `status`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.555871, "duration": 0.00398, "duration_str": "3.98ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 62.566, "width_percent": 1.299}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.560397, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 63.865, "width_percent": 0.134}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.561412, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 63.999, "width_percent": 0.147}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5620139, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 64.146, "width_percent": 0.124}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.56253, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 64.27, "width_percent": 0.127}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.563056, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 64.398, "width_percent": 0.131}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.563587, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 64.528, "width_percent": 0.137}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.564142, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 64.665, "width_percent": 0.127}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.564666, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 64.793, "width_percent": 0.118}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.565143, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 64.91, "width_percent": 0.111}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.565618, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.021, "width_percent": 0.111}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.566101, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.132, "width_percent": 0.114}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.566574, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.246, "width_percent": 0.095}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5669801, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.341, "width_percent": 0.101}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.567403, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.442, "width_percent": 0.091}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5678, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.534, "width_percent": 0.098}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5682192, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.632, "width_percent": 0.091}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.568618, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.723, "width_percent": 0.104}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.569061, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.828, "width_percent": 0.101}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.569557, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.929, "width_percent": 0.111}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.570046, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 66.04, "width_percent": 0.111}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.570606, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 66.151, "width_percent": 0.111}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.571123, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 66.262, "width_percent": 0.124}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.571657, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 66.386, "width_percent": 0.421}, {"sql": "insert into `mp_stores` (`name`, `email`, `phone`, `address`, `country`, `state`, `city`, `zip_code`, `company`, `tax_id`, `description`, `content`, `logo`, `logo_square`, `cover_image`, `certificate_file`, `government_id_file`, `customer_id`, `status`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5767992, "duration": 0.0039700000000000004, "duration_str": "3.97ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 66.807, "width_percent": 1.296}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5813398, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 68.103, "width_percent": 0.131}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.582402, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 68.234, "width_percent": 0.153}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.583066, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 68.387, "width_percent": 0.153}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.583684, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 68.541, "width_percent": 0.121}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.584202, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 68.661, "width_percent": 0.118}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.584696, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 68.779, "width_percent": 0.121}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.585212, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 68.9, "width_percent": 0.127}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.585741, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.027, "width_percent": 0.114}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5862541, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.141, "width_percent": 0.101}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5866961, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.243, "width_percent": 0.095}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.587142, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.337, "width_percent": 0.114}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.587647, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.452, "width_percent": 0.118}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.588138, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.569, "width_percent": 0.108}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.588595, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.677, "width_percent": 0.134}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.589165, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.811, "width_percent": 0.104}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5896, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.915, "width_percent": 0.144}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.590182, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 70.059, "width_percent": 0.124}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.590702, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 70.183, "width_percent": 0.114}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.591183, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 70.297, "width_percent": 0.104}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.591631, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 70.402, "width_percent": 0.111}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.592113, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 70.513, "width_percent": 0.101}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.592545, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 70.614, "width_percent": 0.098}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5929592, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 70.712, "width_percent": 0.091}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.593364, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 70.803, "width_percent": 0.098}, {"sql": "insert into `mp_stores` (`name`, `email`, `phone`, `address`, `country`, `state`, `city`, `zip_code`, `company`, `tax_id`, `description`, `content`, `logo`, `logo_square`, `cover_image`, `certificate_file`, `government_id_file`, `customer_id`, `status`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5973668, "duration": 0.00377, "duration_str": "3.77ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 70.901, "width_percent": 1.231}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6017008, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.132, "width_percent": 0.14}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.602823, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.272, "width_percent": 0.206}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.603704, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.478, "width_percent": 0.173}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6045039, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.651, "width_percent": 0.14}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.605076, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.791, "width_percent": 0.124}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.605603, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.915, "width_percent": 0.18}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.606279, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 73.095, "width_percent": 0.114}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.606745, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 73.209, "width_percent": 0.104}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.607204, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 73.314, "width_percent": 0.124}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.607708, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 73.438, "width_percent": 0.121}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.608197, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 73.559, "width_percent": 0.121}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.608685, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 73.679, "width_percent": 0.118}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.609159, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 73.797, "width_percent": 0.111}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.609616, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 73.908, "width_percent": 0.101}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.610039, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 74.009, "width_percent": 0.101}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.610469, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 74.11, "width_percent": 0.095}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.610882, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 74.205, "width_percent": 0.091}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.611283, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 74.296, "width_percent": 0.091}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.611677, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 74.388, "width_percent": 0.085}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6120558, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 74.473, "width_percent": 0.085}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.612427, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 74.558, "width_percent": 0.085}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6128, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 74.643, "width_percent": 0.085}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6131718, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 74.727, "width_percent": 0.085}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.613544, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 74.812, "width_percent": 0.085}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6139169, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 74.897, "width_percent": 0.085}, {"sql": "insert into `mp_stores` (`name`, `email`, `phone`, `address`, `country`, `state`, `city`, `zip_code`, `company`, `tax_id`, `description`, `content`, `logo`, `logo_square`, `cover_image`, `certificate_file`, `government_id_file`, `customer_id`, `status`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.619503, "duration": 0.00425, "duration_str": "4.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 74.982, "width_percent": 1.388}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6243088, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 76.37, "width_percent": 0.134}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6252391, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 76.503, "width_percent": 0.144}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.625814, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 76.647, "width_percent": 0.121}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.626313, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 76.768, "width_percent": 0.121}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.626814, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 76.889, "width_percent": 0.134}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.627363, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 77.023, "width_percent": 0.131}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.627897, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 77.153, "width_percent": 0.127}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.628419, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 77.28, "width_percent": 0.121}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.628922, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 77.401, "width_percent": 0.108}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.629369, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 77.509, "width_percent": 0.101}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.629792, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 77.61, "width_percent": 0.095}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.630198, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 77.705, "width_percent": 0.091}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.630593, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 77.796, "width_percent": 0.091}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.631014, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 77.888, "width_percent": 0.098}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.631455, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 77.986, "width_percent": 0.111}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.631989, "duration": 0.00225, "duration_str": "2.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 78.097, "width_percent": 0.735}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6345558, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 78.831, "width_percent": 0.189}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.635457, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 79.021, "width_percent": 0.202}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.636365, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 79.223, "width_percent": 0.167}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.637021, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 79.389, "width_percent": 0.121}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.637565, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 79.51, "width_percent": 0.127}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.638206, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 79.638, "width_percent": 0.14}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.638753, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 79.778, "width_percent": 0.127}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.639277, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 79.905, "width_percent": 0.131}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.639813, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 80.036, "width_percent": 0.134}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6403549, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 80.17, "width_percent": 0.14}, {"sql": "insert into `mp_stores` (`name`, `email`, `phone`, `address`, `country`, `state`, `city`, `zip_code`, `company`, `tax_id`, `description`, `content`, `logo`, `logo_square`, `cover_image`, `certificate_file`, `government_id_file`, `customer_id`, `status`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.644499, "duration": 0.00578, "duration_str": "5.78ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 80.31, "width_percent": 1.887}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.650908, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.197, "width_percent": 0.131}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.651896, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.328, "width_percent": 0.153}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6525419, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.481, "width_percent": 0.131}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.653095, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.612, "width_percent": 0.124}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.653655, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.736, "width_percent": 0.131}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.654198, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.866, "width_percent": 0.121}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6546988, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.987, "width_percent": 0.131}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.655252, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.118, "width_percent": 0.114}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.655734, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.232, "width_percent": 0.127}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6562471, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.359, "width_percent": 0.095}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.656659, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.454, "width_percent": 0.124}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.657164, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.578, "width_percent": 0.104}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6575909, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.683, "width_percent": 0.078}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.657935, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.761, "width_percent": 0.078}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6582768, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.839, "width_percent": 0.078}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6586158, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.918, "width_percent": 0.078}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6589558, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.996, "width_percent": 0.078}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.659297, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.074, "width_percent": 0.078}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.659638, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.153, "width_percent": 0.078}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6599789, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.231, "width_percent": 0.078}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.660318, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.31, "width_percent": 0.078}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.660657, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.388, "width_percent": 0.078}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.661005, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.466, "width_percent": 0.091}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6613898, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.558, "width_percent": 0.078}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6617348, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.636, "width_percent": 0.078}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.662068, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.714, "width_percent": 0.082}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.662414, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.796, "width_percent": 0.095}, {"sql": "insert into `mp_stores` (`name`, `email`, `phone`, `address`, `country`, `state`, `city`, `zip_code`, `company`, `tax_id`, `description`, `content`, `logo`, `logo_square`, `cover_image`, `certificate_file`, `government_id_file`, `customer_id`, `status`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.667045, "duration": 0.0039900000000000005, "duration_str": "3.99ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.891, "width_percent": 1.303}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.671593, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.193, "width_percent": 0.14}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.672588, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.334, "width_percent": 0.144}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.673156, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.477, "width_percent": 0.131}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.673693, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.608, "width_percent": 0.127}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.674213, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.735, "width_percent": 0.137}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.674771, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.872, "width_percent": 0.124}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6752858, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.996, "width_percent": 0.134}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.675822, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 87.13, "width_percent": 0.111}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.676279, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 87.241, "width_percent": 0.104}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.676714, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 87.346, "width_percent": 0.104}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.67715, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 87.45, "width_percent": 0.098}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.67756, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 87.548, "width_percent": 0.095}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.677959, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 87.643, "width_percent": 0.095}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6783571, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 87.738, "width_percent": 0.095}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6787581, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 87.832, "width_percent": 0.095}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.679156, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 87.927, "width_percent": 0.095}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6795561, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.022, "width_percent": 0.095}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.679955, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.116, "width_percent": 0.095}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.680375, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.211, "width_percent": 0.095}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.680824, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.306, "width_percent": 0.114}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.681336, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.42, "width_percent": 0.124}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.681916, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.544, "width_percent": 0.111}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.682451, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.655, "width_percent": 0.14}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.68308, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.795, "width_percent": 0.114}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.683592, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.91, "width_percent": 0.104}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.684031, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.014, "width_percent": 0.098}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.684458, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.112, "width_percent": 0.098}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.684885, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.21, "width_percent": 0.111}, {"sql": "insert into `mp_stores` (`name`, `email`, `phone`, `address`, `country`, `state`, `city`, `zip_code`, `company`, `tax_id`, `description`, `content`, `logo`, `logo_square`, `cover_image`, `certificate_file`, `government_id_file`, `customer_id`, `status`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.689323, "duration": 0.004059999999999999, "duration_str": "4.06ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.321, "width_percent": 1.325}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.693867, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.646, "width_percent": 0.118}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6947892, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.764, "width_percent": 0.15}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.695388, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.914, "width_percent": 0.114}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6958728, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.028, "width_percent": 0.14}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.696525, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.169, "width_percent": 0.153}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.697134, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.322, "width_percent": 0.121}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.697629, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.443, "width_percent": 0.121}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6981652, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.564, "width_percent": 0.144}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.698798, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.707, "width_percent": 0.118}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.699306, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.825, "width_percent": 0.104}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.699809, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.929, "width_percent": 0.153}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.700446, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.083, "width_percent": 0.137}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.701001, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.22, "width_percent": 0.118}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.701483, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.338, "width_percent": 0.118}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.701957, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.455, "width_percent": 0.118}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.702453, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.573, "width_percent": 0.118}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.702972, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.69, "width_percent": 0.118}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7034411, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.808, "width_percent": 0.095}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7038422, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.902, "width_percent": 0.098}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.704257, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93, "width_percent": 0.098}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.704661, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.098, "width_percent": 0.085}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7050278, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.183, "width_percent": 0.082}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.705389, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.265, "width_percent": 0.088}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.705766, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.353, "width_percent": 0.085}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7061338, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.438, "width_percent": 0.082}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.706487, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.519, "width_percent": 0.082}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.706836, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.601, "width_percent": 0.082}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7071838, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.683, "width_percent": 0.082}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.707535, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.764, "width_percent": 0.098}, {"sql": "insert into `mp_stores` (`name`, `email`, `phone`, `address`, `country`, `state`, `city`, `zip_code`, `company`, `tax_id`, `description`, `content`, `logo`, `logo_square`, `cover_image`, `certificate_file`, `government_id_file`, `customer_id`, `status`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.712194, "duration": 0.00373, "duration_str": "3.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.862, "width_percent": 1.218}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.71702, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.08, "width_percent": 0.183}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.718175, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.263, "width_percent": 0.131}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.718708, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.393, "width_percent": 0.098}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7191591, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.491, "width_percent": 0.108}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.719664, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.599, "width_percent": 0.095}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.720077, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.694, "width_percent": 0.118}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.720576, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.811, "width_percent": 0.108}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.721054, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.919, "width_percent": 0.121}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.721548, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.04, "width_percent": 0.104}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.721983, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.144, "width_percent": 0.104}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7224262, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.249, "width_percent": 0.098}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.722856, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.347, "width_percent": 0.104}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7233071, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.451, "width_percent": 0.108}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.723777, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.559, "width_percent": 0.104}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7242272, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.663, "width_percent": 0.095}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.724632, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.758, "width_percent": 0.104}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.725075, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.863, "width_percent": 0.091}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.725474, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.954, "width_percent": 0.091}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7258692, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.045, "width_percent": 0.095}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7262762, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.14, "width_percent": 0.091}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.726665, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.231, "width_percent": 0.095}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.727072, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.326, "width_percent": 0.091}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.727457, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.418, "width_percent": 0.095}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.727863, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.512, "width_percent": 0.095}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.728261, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.607, "width_percent": 0.095}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.728661, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.702, "width_percent": 0.095}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.729059, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.796, "width_percent": 0.095}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.729462, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.891, "width_percent": 0.085}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7298439, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.976, "width_percent": 0.085}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.730218, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 98.061, "width_percent": 0.085}, {"sql": "insert into `mp_stores` (`name`, `email`, `phone`, `address`, `country`, `state`, `city`, `zip_code`, `company`, `tax_id`, `description`, `content`, `logo`, `logo_square`, `cover_image`, `certificate_file`, `government_id_file`, `customer_id`, `status`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.737585, "duration": 0.0038799999999999998, "duration_str": "3.88ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 98.146, "width_percent": 1.267}, {"sql": "select * from `ec_customers` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.742021, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.412, "width_percent": 0.118}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.742941, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.53, "width_percent": 0.131}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.743468, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.66, "width_percent": 0.111}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.743928, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.771, "width_percent": 0.111}, {"sql": "select exists(select * from `mp_stores` where `email` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.744394, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.882, "width_percent": 0.118}, {"sql": "... 60924 additional queries are executed but now shown because of Debugbar query limits. Limits can be raised in the config (debugbar.options.db.soft/hard_limit)", "type": "info"}]}, "models": {"data": {"Botble\\Ecommerce\\Models\\Customer": {"value": 348, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "Botble\\Language\\Models\\Language": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "Botble\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 351, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://muhrak.gc/admin/marketplaces/stores/import/import", "action_name": "marketplace.store.import.store", "controller_action": "Botble\\Marketplace\\Http\\Controllers\\ImportStoreController@import", "uri": "POST admin/marketplaces/stores/import/import", "permission": "marketplace.store.import", "controller": "Botble\\Marketplace\\Http\\Controllers\\ImportStoreController@import<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fvendor%2Fbotble%2Fdata-synchronize%2Fsrc%2FHttp%2FControllers%2FImportController.php&line=70\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Botble\\Marketplace\\Http\\Controllers", "prefix": "admin/marketplaces/stores/import", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fvendor%2Fbotble%2Fdata-synchronize%2Fsrc%2FHttp%2FControllers%2FImportController.php&line=70\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/botble/data-synchronize/src/Http/Controllers/ImportController.php:70-118</a>", "middleware": "web, core, auth", "duration": "36.68s", "peak_memory": "54MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1786958076 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1786958076\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-696122953 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FtY4EDLj9P6dViTp4GrWt6OFO4QWGtrBz9X4LzQ3</span>\"\n  \"<span class=sf-dump-key>chunk_size</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1000</span>\"\n  \"<span class=sf-dump-key>file_name</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Stores-example-a7072ba7d6642ad490696349bb2cd3e5-684c5ce8bb3a2.csv</span>\"\n  \"<span class=sf-dump-key>offset</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>limit</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1000</span>\"\n  \"<span class=sf-dump-key>total</span>\" => \"<span class=sf-dump-str>0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-696122953\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-786447409 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IlBaZ0VvbmZFQW9UMkptWUM5bFhDSmc9PSIsInZhbHVlIjoiTG1TQlZrcGpodU9TampKME9XOGhqMXBkcGlOcGVtZUZ6UG9uQUZORXAwdFp1WThKVVhPRGdJYldrQXlNcUF2M2hUa20vdUM0ckkxQzFKTTBKTGJTenErTUREaWh6djAzMUJZdlRHdHVTZFN4V0lGN1dGTHlOOGY3R051bFNRenciLCJtYWMiOiJiYjRmZjkwOTg5YmY2ZWRkNWY1NTk1ZTc1ZDI1Mzc3MzEzNjcxYjA0NDNiM2YyZTJkOTU0NDhkMGM2ZTNiZmQ3IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">muhrak.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">722</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryEEoVQRnFjscOLlOp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">https://muhrak.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">https://muhrak.gc/admin/marketplaces/stores/import</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3140 characters\">cookie_for_consent=1; botble_footprints_cookie=eyJpdiI6InJtenpwcG01ZldyRCs2TFV4YUYrYUE9PSIsInZhbHVlIjoiamVKQ2MwT2VxdDI3L0pXTXdlMURsY0JJSHdaWVp5T1VoSUNSbEpIZ0pZcHdKWGZId3lBLzJ2b1pIUXVYUUxKRjdMY3k5Q1VpTWRFd3Ftbzd6L3UyeHUvdWFpdTAwUXBNZER4MDc5MGJjOXQxQXhIazNiaTI0TU42YXJkbHNVek8iLCJtYWMiOiI3YWM1Njc0ODBhMzFhMjBlOWM2NzQ2YTE1ODRkNGM0ODk5YTNhYjE5ZjliNjY3NGI2MzA5MjMxNGJhNDliZjJjIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6ImozUjJ6OXN5RkhxbXdCYmc5K1JURlE9PSIsInZhbHVlIjoib3plTVp0NnFEZVhoU1NQejZBMklhY2RqSEtwTmNvaG1SeWtpbFkvZmVZb21QUmg4VTFvRVlPbUUzd1NGQ3J6dFZVT0hmdDIrTWd0SGF1Y28yYWx1VWJYdTJ0OUFvdkxJM3U3UktZNXpBL1pReU8xcDI0S3AxSmRkVzVHaHhyZHlia1ptb0wrTUhkNHRPbXhoY2pNL2JoZ2JXVGNTaHI2VjRIRW1HOUdiVHAwN2xxdkJLazdwZDFadCtJaTNlTk9HT2JmWms2Vnp5bGorWkxFd1dvaStjUHlEY1ZQR2tYOW5tREUxV25Vek1jbDFkenJlZENXR3VIaHM4aFdLaDRVNjViY21idXByU3ZQQVFCU2gxcVNnYW5zbERUamgxZjNUMVRIWEt4V0tUbGxFRDc5VzhDNkZRZ0g1QXRjU3Rna3ZTQlRwZjVScFM1eEdzUmNhSlAxaHpIZ2kxdVEvOG51K1FlVmhuSTBGdUZxYXdkdGdWLzFDdW1nZ0MvdDFnTit6STdZd3JJWnVCNGZZSTNFRWtjaTJndzZVT3prNEkrb1c0cTFFaGFWckxiUUtVSFVsYXlWSWxnUSttR2dxYlo0K0dCY1NzRDBGdzVNZGdLTnFOZG43VllRTkFORnIrZlk1SEt3ZFkvNjYwdjJFWmFJaCtPUVh4TjFZejFleUQvU09oeERvVkdyVVJ2d0ZkM25TZXU1OGNZOE5PWTJWVmV4WWpsWUE2Zmc0dHV3PSIsIm1hYyI6IjYwMTc0ODhiZDNjN2VkZGVmZjliNmYyNWU0ZTU2ZWE5YjhkMjllNzk1ZDAxNGU2ZWU3MDhkOTA3NzM3OTFjNjUiLCJ0YWciOiIifQ%3D%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IktsRlR4UnBrM0szS1Y0SUxrVVlUdHc9PSIsInZhbHVlIjoibVgzWjBpSHR4T2o4NlJBc3ZWNEVGZGVXS0xzU3NlNjRPOXFyNVFIOVVkbCt1ODRxbVZFNkNaODlSa3BORzF3bTJEb3Rkb0RVTVVjZHNBVFZiVnQ2TlhLOVBNVkJhVUIzb0ZZV2VTOER2dEJjd1ZZZEw3OWg4UEV4OXZqSjQwR2hsbERhOTdCQ1puTTJGRG1nMnozejdpcXp0NllsWUVzYWl5SjFlZGhYVWdUNmYyY2xRVXdXeG9UU3M3S1kxMnVxbVc5bktlTHFEOHNJbDlUV2JPamxrK3AwangreXlUMmtBTVhRdlN0SHQwcz0iLCJtYWMiOiIzNTliMzJjOTQxODc2NDdkNDI0YTBiNjZkMzUzZjEzNzZjYWMwYzlmNDQ2NGZhZjJiOTk5M2NhYTc0YmEyOGY5IiwidGFnIjoiIn0%3D; remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ijl6MUxxZXpoME96cm9sVGNGeUlyaEE9PSIsInZhbHVlIjoiWlR1dnBBRnpBZWF2c1I3dndid2M5UllhSUVPN0Fqa1p0K0RGelpiVkRpditYckx0YWZ2RVZFbTMwUGVnVGh4ODc5VFBseUtFaytWNWpRalB1QVhJbHZONFhqaFc2UjdjTlI1SlR0TDAwMS9ldmZxN1F5aTZpaWdnaFNOaGs0VmRWMlBoNkhMeTZKSU1hVTA3UHRKNUJKb3hPUEJVUWtEcjc2RFZmQ3NVanJEeHNHMU1jc3QzQ2N5blJKUUJVaFNsUEFQSmVKSHBiTjhtVmU2OEUwY1dBMTNyblVPOENzcTlRcHBOOTVuTWc1OD0iLCJtYWMiOiJhNzJhNzEyNGRjOTM0ZmVhMTIyNDU0ZmM0Mzc2MDQzZjI3MjNjNmM4NTUwMGQ2ZTZlNmZjM2NlMWZkYTgyMGM0IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlBaZ0VvbmZFQW9UMkptWUM5bFhDSmc9PSIsInZhbHVlIjoiTG1TQlZrcGpodU9TampKME9XOGhqMXBkcGlOcGVtZUZ6UG9uQUZORXAwdFp1WThKVVhPRGdJYldrQXlNcUF2M2hUa20vdUM0ckkxQzFKTTBKTGJTenErTUREaWh6djAzMUJZdlRHdHVTZFN4V0lGN1dGTHlOOGY3R051bFNRenciLCJtYWMiOiJiYjRmZjkwOTg5YmY2ZWRkNWY1NTk1ZTc1ZDI1Mzc3MzEzNjcxYjA0NDNiM2YyZTJkOTU0NDhkMGM2ZTNiZmQ3IiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6IjB5OERyeElaZDB3cmRyUXhUMGZreUE9PSIsInZhbHVlIjoiTEtwZ0lsT29pQk90TTNNOFBRVUVEVzVkWjFWUmRuWTdiSEZTb3IzTUIrRWZWaXRQNHVvc0V0SlI2VXJXb282cUtFUFNxYVBvZ1NtYTR2RTFOcnlTcTMrNEpKUE1mdlNXZU1rT09YUVFCTHAyRGgvYVU2OERuUkZuQmVtdTFOWnQiLCJtYWMiOiI4YTdmOGM3M2IwNDQ3ZGVjY2NiNjg4YjI2ODViZWNlNWE5MjRhNGYyZTBkY2YyMzdiN2YzYTgyMDhmM2U1MjI4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-786447409\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">14908b951300852119d2c46221b4b11a7e2b8107</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"359 characters\">{&quot;footprint&quot;:&quot;14908b951300852119d2c46221b4b11a7e2b8107&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;muhrak.gc&quot;,&quot;landing_page&quot;:&quot;install\\/welcome&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|lEoydfVrZKIFerelBjYotnW4P2e0TuoqJSFCOOr89XMprC6ygbibdEjl3rvX|$2y$12$6oEMzkNhsgbeo4WDg72G9e2nWxuvMvozEUdkIQ0AfpRz3PJNQ7XzG</span>\"\n  \"<span class=sf-dump-key>remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">2|eZ0awTe8bfQLceQTos5gvZ7R55kW3IiLBR997mfYDDJcPndE2TcWxwpqf39P|$2y$12$EU5h35Jju9igRCRES6rqCuE42l30uwJ94rSesc56rboRI0xQqYy/G</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FtY4EDLj9P6dViTp4GrWt6OFO4QWGtrBz9X4LzQ3</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7jtlU2dXcBjwVHs9Eymy9yeCbICkdDlwWf2uJ6r5</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 13 Jun 2025 17:17:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1060763413 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FtY4EDLj9P6dViTp4GrWt6OFO4QWGtrBz9X4LzQ3</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"50 characters\">https://muhrak.gc/admin/marketplaces/stores/import</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1060763413\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://muhrak.gc/admin/marketplaces/stores/import/import", "action_name": "marketplace.store.import.store", "controller_action": "Botble\\Marketplace\\Http\\Controllers\\ImportStoreController@import"}, "badge": null}}