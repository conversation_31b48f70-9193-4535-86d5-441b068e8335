{"__meta": {"id": "01JXN4V8SE5HG0ABX12AFS4K9F", "datetime": "2025-06-13 17:19:22", "utime": **********.414899, "method": "POST", "uri": "/admin/marketplaces/stores/edit/412", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749835160.660754, "end": **********.414917, "duration": 1.7541630268096924, "duration_str": "1.75s", "measures": [{"label": "Booting", "start": 1749835160.660754, "relative_start": 0, "end": **********.592475, "relative_end": **********.592475, "duration": 0.****************, "duration_str": "932ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.592497, "relative_start": 0.****************, "end": **********.41492, "relative_end": 3.0994415283203125e-06, "duration": 0.***************, "duration_str": "822ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.614589, "relative_start": 0.****************, "end": **********.6271, "relative_end": **********.6271, "duration": 0.012511014938354492, "duration_str": "12.51ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: plugins/marketplace::partials.extra-content", "start": **********.77979, "relative_start": 1.****************, "end": **********.77979, "relative_end": **********.77979, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0226480071dd0e74b211507465ab2265", "start": **********.790047, "relative_start": 1.****************, "end": **********.790047, "relative_end": **********.790047, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6112de9dcda50c4e5e8edc1897f5a562", "start": **********.792077, "relative_start": 1.1313230991363525, "end": **********.792077, "relative_end": **********.792077, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1f7684c3b342ae4833b715597e999b14", "start": **********.793986, "relative_start": 1.1332321166992188, "end": **********.793986, "relative_end": **********.793986, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::72031399d6d0521c81f2b93c5dd13aa0", "start": **********.796004, "relative_start": 1.1352500915527344, "end": **********.796004, "relative_end": **********.796004, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::3fb0a03fd317b61c1c5c6c5fdfca9fed", "start": **********.797993, "relative_start": 1.1372389793395996, "end": **********.797993, "relative_end": **********.797993, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::19d2357ba0f51b29e340109a50dc8d90", "start": **********.80306, "relative_start": 1.1423060894012451, "end": **********.80306, "relative_end": **********.80306, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::40d3ae0c6564c877b76ae3dc338ae7c1", "start": **********.804975, "relative_start": 1.1442210674285889, "end": **********.804975, "relative_end": **********.804975, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::cd814d5b6b7772dea2491712766aab3a", "start": **********.808884, "relative_start": 1.148129940032959, "end": **********.808884, "relative_end": **********.808884, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0a9241f493369d6396bf7b7659ea810a", "start": **********.81306, "relative_start": 1.152306079864502, "end": **********.81306, "relative_end": **********.81306, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::166bf66f7bb469356a900c073c2a06b1", "start": **********.816726, "relative_start": 1.1559720039367676, "end": **********.816726, "relative_end": **********.816726, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::69c1a9f63ddda762935876cbaefcfeb9", "start": **********.820279, "relative_start": 1.159524917602539, "end": **********.820279, "relative_end": **********.820279, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::64186fa289f5b3c8518f3b21cd8fdc36", "start": **********.82537, "relative_start": 1.1646161079406738, "end": **********.82537, "relative_end": **********.82537, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b13cc8f2acf7f8ddf927c806fc8761af", "start": **********.828733, "relative_start": 1.1679790019989014, "end": **********.828733, "relative_end": **********.828733, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::af41809cec0480ce33486a19dae27e58", "start": **********.830698, "relative_start": 1.1699440479278564, "end": **********.830698, "relative_end": **********.830698, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::288aa1fd33c927bf1a725359e71933aa", "start": **********.834134, "relative_start": 1.1733801364898682, "end": **********.834134, "relative_end": **********.834134, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::table-info", "start": **********.865822, "relative_start": 1.2050681114196777, "end": **********.865822, "relative_end": **********.865822, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0343a1b0800146d7d9cf6a9514ec7bf4", "start": **********.866824, "relative_start": 1.2060699462890625, "end": **********.866824, "relative_end": **********.866824, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::badge", "start": **********.867371, "relative_start": 1.2066171169281006, "end": **********.867371, "relative_end": **********.867371, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/marketplace::partials.header-actions", "start": **********.870199, "relative_start": 1.2094449996948242, "end": **********.870199, "relative_end": **********.870199, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::button", "start": **********.871292, "relative_start": 1.2105381488800049, "end": **********.871292, "relative_end": **********.871292, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d9cf6f2adf46159095e24cd6ce9ab42b", "start": **********.872747, "relative_start": 1.2119929790496826, "end": **********.872747, "relative_end": **********.872747, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::button", "start": **********.873288, "relative_start": 1.212533950805664, "end": **********.873288, "relative_end": **********.873288, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d25e365b684c7fa9ad5ec13cfb768fc1", "start": **********.874696, "relative_start": 1.213942050933838, "end": **********.874696, "relative_end": **********.874696, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/marketplace::partials.extra-content", "start": **********.94445, "relative_start": 1.283695936203003, "end": **********.94445, "relative_end": **********.94445, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0226480071dd0e74b211507465ab2265", "start": **********.946163, "relative_start": 1.2854089736938477, "end": **********.946163, "relative_end": **********.946163, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6112de9dcda50c4e5e8edc1897f5a562", "start": **********.947488, "relative_start": 1.2867341041564941, "end": **********.947488, "relative_end": **********.947488, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1f7684c3b342ae4833b715597e999b14", "start": **********.948787, "relative_start": 1.2880330085754395, "end": **********.948787, "relative_end": **********.948787, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::72031399d6d0521c81f2b93c5dd13aa0", "start": **********.950078, "relative_start": 1.2893240451812744, "end": **********.950078, "relative_end": **********.950078, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::3fb0a03fd317b61c1c5c6c5fdfca9fed", "start": **********.951358, "relative_start": 1.2906041145324707, "end": **********.951358, "relative_end": **********.951358, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::19d2357ba0f51b29e340109a50dc8d90", "start": **********.952651, "relative_start": 1.2918970584869385, "end": **********.952651, "relative_end": **********.952651, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::40d3ae0c6564c877b76ae3dc338ae7c1", "start": **********.95393, "relative_start": 1.2931759357452393, "end": **********.95393, "relative_end": **********.95393, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::cd814d5b6b7772dea2491712766aab3a", "start": **********.955236, "relative_start": 1.2944819927215576, "end": **********.955236, "relative_end": **********.955236, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0a9241f493369d6396bf7b7659ea810a", "start": **********.956649, "relative_start": 1.2958950996398926, "end": **********.956649, "relative_end": **********.956649, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::166bf66f7bb469356a900c073c2a06b1", "start": **********.958274, "relative_start": 1.2975199222564697, "end": **********.958274, "relative_end": **********.958274, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::69c1a9f63ddda762935876cbaefcfeb9", "start": **********.959632, "relative_start": 1.2988779544830322, "end": **********.959632, "relative_end": **********.959632, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::64186fa289f5b3c8518f3b21cd8fdc36", "start": **********.960974, "relative_start": 1.300220012664795, "end": **********.960974, "relative_end": **********.960974, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b13cc8f2acf7f8ddf927c806fc8761af", "start": **********.962299, "relative_start": 1.3015451431274414, "end": **********.962299, "relative_end": **********.962299, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::af41809cec0480ce33486a19dae27e58", "start": **********.963627, "relative_start": 1.302873134613037, "end": **********.963627, "relative_end": **********.963627, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::288aa1fd33c927bf1a725359e71933aa", "start": **********.964949, "relative_start": 1.3041949272155762, "end": **********.964949, "relative_end": **********.964949, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::table-info", "start": **********.972275, "relative_start": 1.311521053314209, "end": **********.972275, "relative_end": **********.972275, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0343a1b0800146d7d9cf6a9514ec7bf4", "start": **********.972914, "relative_start": 1.3121600151062012, "end": **********.972914, "relative_end": **********.972914, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::badge", "start": **********.973287, "relative_start": 1.3125331401824951, "end": **********.973287, "relative_end": **********.973287, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/marketplace::partials.header-actions", "start": **********.974891, "relative_start": 1.3141369819641113, "end": **********.974891, "relative_end": **********.974891, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::button", "start": **********.975728, "relative_start": 1.314974069595337, "end": **********.975728, "relative_end": **********.975728, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d9cf6f2adf46159095e24cd6ce9ab42b", "start": **********.97644, "relative_start": 1.3156859874725342, "end": **********.97644, "relative_end": **********.97644, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::button", "start": **********.976803, "relative_start": 1.3160490989685059, "end": **********.976803, "relative_end": **********.976803, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d25e365b684c7fa9ad5ec13cfb768fc1", "start": **********.977461, "relative_start": 1.3167071342468262, "end": **********.977461, "relative_end": **********.977461, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.349314, "relative_start": 1.6885600090026855, "end": **********.412491, "relative_end": **********.412491, "duration": 0.06317710876464844, "duration_str": "63.18ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 57025232, "peak_usage_str": "54MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.3.17", "Environment": "localhost", "Debug Mode": "Enabled", "URL": "muhrak.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 48, "nb_templates": 48, "templates": [{"name": "plugins/marketplace::partials.extra-content", "param_count": null, "params": [], "start": **********.77974, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/plugins/marketplace/resources/views/partials/extra-content.blade.phpplugins/marketplace::partials.extra-content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fresources%2Fviews%2Fpartials%2Fextra-content.blade.php&line=1", "ajax": false, "filename": "extra-content.blade.php", "line": "?"}}, {"name": "__components::0226480071dd0e74b211507465ab2265", "param_count": null, "params": [], "start": **********.790022, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/0226480071dd0e74b211507465ab2265.blade.php__components::0226480071dd0e74b211507465ab2265", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F0226480071dd0e74b211507465ab2265.blade.php&line=1", "ajax": false, "filename": "0226480071dd0e74b211507465ab2265.blade.php", "line": "?"}}, {"name": "__components::6112de9dcda50c4e5e8edc1897f5a562", "param_count": null, "params": [], "start": **********.792056, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/6112de9dcda50c4e5e8edc1897f5a562.blade.php__components::6112de9dcda50c4e5e8edc1897f5a562", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F6112de9dcda50c4e5e8edc1897f5a562.blade.php&line=1", "ajax": false, "filename": "6112de9dcda50c4e5e8edc1897f5a562.blade.php", "line": "?"}}, {"name": "__components::1f7684c3b342ae4833b715597e999b14", "param_count": null, "params": [], "start": **********.793965, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/1f7684c3b342ae4833b715597e999b14.blade.php__components::1f7684c3b342ae4833b715597e999b14", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F1f7684c3b342ae4833b715597e999b14.blade.php&line=1", "ajax": false, "filename": "1f7684c3b342ae4833b715597e999b14.blade.php", "line": "?"}}, {"name": "__components::72031399d6d0521c81f2b93c5dd13aa0", "param_count": null, "params": [], "start": **********.795982, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/72031399d6d0521c81f2b93c5dd13aa0.blade.php__components::72031399d6d0521c81f2b93c5dd13aa0", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F72031399d6d0521c81f2b93c5dd13aa0.blade.php&line=1", "ajax": false, "filename": "72031399d6d0521c81f2b93c5dd13aa0.blade.php", "line": "?"}}, {"name": "__components::3fb0a03fd317b61c1c5c6c5fdfca9fed", "param_count": null, "params": [], "start": **********.797971, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/3fb0a03fd317b61c1c5c6c5fdfca9fed.blade.php__components::3fb0a03fd317b61c1c5c6c5fdfca9fed", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F3fb0a03fd317b61c1c5c6c5fdfca9fed.blade.php&line=1", "ajax": false, "filename": "3fb0a03fd317b61c1c5c6c5fdfca9fed.blade.php", "line": "?"}}, {"name": "__components::19d2357ba0f51b29e340109a50dc8d90", "param_count": null, "params": [], "start": **********.803036, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/19d2357ba0f51b29e340109a50dc8d90.blade.php__components::19d2357ba0f51b29e340109a50dc8d90", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F19d2357ba0f51b29e340109a50dc8d90.blade.php&line=1", "ajax": false, "filename": "19d2357ba0f51b29e340109a50dc8d90.blade.php", "line": "?"}}, {"name": "__components::40d3ae0c6564c877b76ae3dc338ae7c1", "param_count": null, "params": [], "start": **********.804953, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/40d3ae0c6564c877b76ae3dc338ae7c1.blade.php__components::40d3ae0c6564c877b76ae3dc338ae7c1", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F40d3ae0c6564c877b76ae3dc338ae7c1.blade.php&line=1", "ajax": false, "filename": "40d3ae0c6564c877b76ae3dc338ae7c1.blade.php", "line": "?"}}, {"name": "__components::cd814d5b6b7772dea2491712766aab3a", "param_count": null, "params": [], "start": **********.80886, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/cd814d5b6b7772dea2491712766aab3a.blade.php__components::cd814d5b6b7772dea2491712766aab3a", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fcd814d5b6b7772dea2491712766aab3a.blade.php&line=1", "ajax": false, "filename": "cd814d5b6b7772dea2491712766aab3a.blade.php", "line": "?"}}, {"name": "__components::0a9241f493369d6396bf7b7659ea810a", "param_count": null, "params": [], "start": **********.813038, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/0a9241f493369d6396bf7b7659ea810a.blade.php__components::0a9241f493369d6396bf7b7659ea810a", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F0a9241f493369d6396bf7b7659ea810a.blade.php&line=1", "ajax": false, "filename": "0a9241f493369d6396bf7b7659ea810a.blade.php", "line": "?"}}, {"name": "__components::166bf66f7bb469356a900c073c2a06b1", "param_count": null, "params": [], "start": **********.816703, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/166bf66f7bb469356a900c073c2a06b1.blade.php__components::166bf66f7bb469356a900c073c2a06b1", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F166bf66f7bb469356a900c073c2a06b1.blade.php&line=1", "ajax": false, "filename": "166bf66f7bb469356a900c073c2a06b1.blade.php", "line": "?"}}, {"name": "__components::69c1a9f63ddda762935876cbaefcfeb9", "param_count": null, "params": [], "start": **********.820256, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/69c1a9f63ddda762935876cbaefcfeb9.blade.php__components::69c1a9f63ddda762935876cbaefcfeb9", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F69c1a9f63ddda762935876cbaefcfeb9.blade.php&line=1", "ajax": false, "filename": "69c1a9f63ddda762935876cbaefcfeb9.blade.php", "line": "?"}}, {"name": "__components::64186fa289f5b3c8518f3b21cd8fdc36", "param_count": null, "params": [], "start": **********.825345, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/64186fa289f5b3c8518f3b21cd8fdc36.blade.php__components::64186fa289f5b3c8518f3b21cd8fdc36", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F64186fa289f5b3c8518f3b21cd8fdc36.blade.php&line=1", "ajax": false, "filename": "64186fa289f5b3c8518f3b21cd8fdc36.blade.php", "line": "?"}}, {"name": "__components::b13cc8f2acf7f8ddf927c806fc8761af", "param_count": null, "params": [], "start": **********.82871, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/b13cc8f2acf7f8ddf927c806fc8761af.blade.php__components::b13cc8f2acf7f8ddf927c806fc8761af", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fb13cc8f2acf7f8ddf927c806fc8761af.blade.php&line=1", "ajax": false, "filename": "b13cc8f2acf7f8ddf927c806fc8761af.blade.php", "line": "?"}}, {"name": "__components::af41809cec0480ce33486a19dae27e58", "param_count": null, "params": [], "start": **********.830677, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/af41809cec0480ce33486a19dae27e58.blade.php__components::af41809cec0480ce33486a19dae27e58", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Faf41809cec0480ce33486a19dae27e58.blade.php&line=1", "ajax": false, "filename": "af41809cec0480ce33486a19dae27e58.blade.php", "line": "?"}}, {"name": "__components::288aa1fd33c927bf1a725359e71933aa", "param_count": null, "params": [], "start": **********.834112, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/288aa1fd33c927bf1a725359e71933aa.blade.php__components::288aa1fd33c927bf1a725359e71933aa", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F288aa1fd33c927bf1a725359e71933aa.blade.php&line=1", "ajax": false, "filename": "288aa1fd33c927bf1a725359e71933aa.blade.php", "line": "?"}}, {"name": "core/table::table-info", "param_count": null, "params": [], "start": **********.865799, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/table/resources/views/table-info.blade.phpcore/table::table-info", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Ftable-info.blade.php&line=1", "ajax": false, "filename": "table-info.blade.php", "line": "?"}}, {"name": "__components::0343a1b0800146d7d9cf6a9514ec7bf4", "param_count": null, "params": [], "start": **********.866802, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/0343a1b0800146d7d9cf6a9514ec7bf4.blade.php__components::0343a1b0800146d7d9cf6a9514ec7bf4", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F0343a1b0800146d7d9cf6a9514ec7bf4.blade.php&line=1", "ajax": false, "filename": "0343a1b0800146d7d9cf6a9514ec7bf4.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::badge", "param_count": null, "params": [], "start": **********.867351, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/badge.blade.phpa74ad8dfacd4f985eb3977517615ce25::badge", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fbadge.blade.php&line=1", "ajax": false, "filename": "badge.blade.php", "line": "?"}}, {"name": "plugins/marketplace::partials.header-actions", "param_count": null, "params": [], "start": **********.870175, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/plugins/marketplace/resources/views/partials/header-actions.blade.phpplugins/marketplace::partials.header-actions", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fresources%2Fviews%2Fpartials%2Fheader-actions.blade.php&line=1", "ajax": false, "filename": "header-actions.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::button", "param_count": null, "params": [], "start": **********.871263, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/button.blade.phpa74ad8dfacd4f985eb3977517615ce25::button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}}, {"name": "__components::d9cf6f2adf46159095e24cd6ce9ab42b", "param_count": null, "params": [], "start": **********.872697, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/d9cf6f2adf46159095e24cd6ce9ab42b.blade.php__components::d9cf6f2adf46159095e24cd6ce9ab42b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fd9cf6f2adf46159095e24cd6ce9ab42b.blade.php&line=1", "ajax": false, "filename": "d9cf6f2adf46159095e24cd6ce9ab42b.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::button", "param_count": null, "params": [], "start": **********.873267, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/button.blade.phpa74ad8dfacd4f985eb3977517615ce25::button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}}, {"name": "__components::d25e365b684c7fa9ad5ec13cfb768fc1", "param_count": null, "params": [], "start": **********.874674, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/d25e365b684c7fa9ad5ec13cfb768fc1.blade.php__components::d25e365b684c7fa9ad5ec13cfb768fc1", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fd25e365b684c7fa9ad5ec13cfb768fc1.blade.php&line=1", "ajax": false, "filename": "d25e365b684c7fa9ad5ec13cfb768fc1.blade.php", "line": "?"}}, {"name": "plugins/marketplace::partials.extra-content", "param_count": null, "params": [], "start": **********.944427, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/plugins/marketplace/resources/views/partials/extra-content.blade.phpplugins/marketplace::partials.extra-content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fresources%2Fviews%2Fpartials%2Fextra-content.blade.php&line=1", "ajax": false, "filename": "extra-content.blade.php", "line": "?"}}, {"name": "__components::0226480071dd0e74b211507465ab2265", "param_count": null, "params": [], "start": **********.946141, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/0226480071dd0e74b211507465ab2265.blade.php__components::0226480071dd0e74b211507465ab2265", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F0226480071dd0e74b211507465ab2265.blade.php&line=1", "ajax": false, "filename": "0226480071dd0e74b211507465ab2265.blade.php", "line": "?"}}, {"name": "__components::6112de9dcda50c4e5e8edc1897f5a562", "param_count": null, "params": [], "start": **********.947467, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/6112de9dcda50c4e5e8edc1897f5a562.blade.php__components::6112de9dcda50c4e5e8edc1897f5a562", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F6112de9dcda50c4e5e8edc1897f5a562.blade.php&line=1", "ajax": false, "filename": "6112de9dcda50c4e5e8edc1897f5a562.blade.php", "line": "?"}}, {"name": "__components::1f7684c3b342ae4833b715597e999b14", "param_count": null, "params": [], "start": **********.948767, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/1f7684c3b342ae4833b715597e999b14.blade.php__components::1f7684c3b342ae4833b715597e999b14", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F1f7684c3b342ae4833b715597e999b14.blade.php&line=1", "ajax": false, "filename": "1f7684c3b342ae4833b715597e999b14.blade.php", "line": "?"}}, {"name": "__components::72031399d6d0521c81f2b93c5dd13aa0", "param_count": null, "params": [], "start": **********.950058, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/72031399d6d0521c81f2b93c5dd13aa0.blade.php__components::72031399d6d0521c81f2b93c5dd13aa0", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F72031399d6d0521c81f2b93c5dd13aa0.blade.php&line=1", "ajax": false, "filename": "72031399d6d0521c81f2b93c5dd13aa0.blade.php", "line": "?"}}, {"name": "__components::3fb0a03fd317b61c1c5c6c5fdfca9fed", "param_count": null, "params": [], "start": **********.951338, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/3fb0a03fd317b61c1c5c6c5fdfca9fed.blade.php__components::3fb0a03fd317b61c1c5c6c5fdfca9fed", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F3fb0a03fd317b61c1c5c6c5fdfca9fed.blade.php&line=1", "ajax": false, "filename": "3fb0a03fd317b61c1c5c6c5fdfca9fed.blade.php", "line": "?"}}, {"name": "__components::19d2357ba0f51b29e340109a50dc8d90", "param_count": null, "params": [], "start": **********.952631, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/19d2357ba0f51b29e340109a50dc8d90.blade.php__components::19d2357ba0f51b29e340109a50dc8d90", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F19d2357ba0f51b29e340109a50dc8d90.blade.php&line=1", "ajax": false, "filename": "19d2357ba0f51b29e340109a50dc8d90.blade.php", "line": "?"}}, {"name": "__components::40d3ae0c6564c877b76ae3dc338ae7c1", "param_count": null, "params": [], "start": **********.95391, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/40d3ae0c6564c877b76ae3dc338ae7c1.blade.php__components::40d3ae0c6564c877b76ae3dc338ae7c1", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F40d3ae0c6564c877b76ae3dc338ae7c1.blade.php&line=1", "ajax": false, "filename": "40d3ae0c6564c877b76ae3dc338ae7c1.blade.php", "line": "?"}}, {"name": "__components::cd814d5b6b7772dea2491712766aab3a", "param_count": null, "params": [], "start": **********.955215, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/cd814d5b6b7772dea2491712766aab3a.blade.php__components::cd814d5b6b7772dea2491712766aab3a", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fcd814d5b6b7772dea2491712766aab3a.blade.php&line=1", "ajax": false, "filename": "cd814d5b6b7772dea2491712766aab3a.blade.php", "line": "?"}}, {"name": "__components::0a9241f493369d6396bf7b7659ea810a", "param_count": null, "params": [], "start": **********.956617, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/0a9241f493369d6396bf7b7659ea810a.blade.php__components::0a9241f493369d6396bf7b7659ea810a", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F0a9241f493369d6396bf7b7659ea810a.blade.php&line=1", "ajax": false, "filename": "0a9241f493369d6396bf7b7659ea810a.blade.php", "line": "?"}}, {"name": "__components::166bf66f7bb469356a900c073c2a06b1", "param_count": null, "params": [], "start": **********.958252, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/166bf66f7bb469356a900c073c2a06b1.blade.php__components::166bf66f7bb469356a900c073c2a06b1", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F166bf66f7bb469356a900c073c2a06b1.blade.php&line=1", "ajax": false, "filename": "166bf66f7bb469356a900c073c2a06b1.blade.php", "line": "?"}}, {"name": "__components::69c1a9f63ddda762935876cbaefcfeb9", "param_count": null, "params": [], "start": **********.959611, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/69c1a9f63ddda762935876cbaefcfeb9.blade.php__components::69c1a9f63ddda762935876cbaefcfeb9", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F69c1a9f63ddda762935876cbaefcfeb9.blade.php&line=1", "ajax": false, "filename": "69c1a9f63ddda762935876cbaefcfeb9.blade.php", "line": "?"}}, {"name": "__components::64186fa289f5b3c8518f3b21cd8fdc36", "param_count": null, "params": [], "start": **********.960953, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/64186fa289f5b3c8518f3b21cd8fdc36.blade.php__components::64186fa289f5b3c8518f3b21cd8fdc36", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F64186fa289f5b3c8518f3b21cd8fdc36.blade.php&line=1", "ajax": false, "filename": "64186fa289f5b3c8518f3b21cd8fdc36.blade.php", "line": "?"}}, {"name": "__components::b13cc8f2acf7f8ddf927c806fc8761af", "param_count": null, "params": [], "start": **********.962279, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/b13cc8f2acf7f8ddf927c806fc8761af.blade.php__components::b13cc8f2acf7f8ddf927c806fc8761af", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fb13cc8f2acf7f8ddf927c806fc8761af.blade.php&line=1", "ajax": false, "filename": "b13cc8f2acf7f8ddf927c806fc8761af.blade.php", "line": "?"}}, {"name": "__components::af41809cec0480ce33486a19dae27e58", "param_count": null, "params": [], "start": **********.963607, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/af41809cec0480ce33486a19dae27e58.blade.php__components::af41809cec0480ce33486a19dae27e58", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Faf41809cec0480ce33486a19dae27e58.blade.php&line=1", "ajax": false, "filename": "af41809cec0480ce33486a19dae27e58.blade.php", "line": "?"}}, {"name": "__components::288aa1fd33c927bf1a725359e71933aa", "param_count": null, "params": [], "start": **********.964929, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/288aa1fd33c927bf1a725359e71933aa.blade.php__components::288aa1fd33c927bf1a725359e71933aa", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F288aa1fd33c927bf1a725359e71933aa.blade.php&line=1", "ajax": false, "filename": "288aa1fd33c927bf1a725359e71933aa.blade.php", "line": "?"}}, {"name": "core/table::table-info", "param_count": null, "params": [], "start": **********.972249, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/table/resources/views/table-info.blade.phpcore/table::table-info", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Ftable-info.blade.php&line=1", "ajax": false, "filename": "table-info.blade.php", "line": "?"}}, {"name": "__components::0343a1b0800146d7d9cf6a9514ec7bf4", "param_count": null, "params": [], "start": **********.972892, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/0343a1b0800146d7d9cf6a9514ec7bf4.blade.php__components::0343a1b0800146d7d9cf6a9514ec7bf4", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F0343a1b0800146d7d9cf6a9514ec7bf4.blade.php&line=1", "ajax": false, "filename": "0343a1b0800146d7d9cf6a9514ec7bf4.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::badge", "param_count": null, "params": [], "start": **********.973267, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/badge.blade.phpa74ad8dfacd4f985eb3977517615ce25::badge", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fbadge.blade.php&line=1", "ajax": false, "filename": "badge.blade.php", "line": "?"}}, {"name": "plugins/marketplace::partials.header-actions", "param_count": null, "params": [], "start": **********.97487, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/plugins/marketplace/resources/views/partials/header-actions.blade.phpplugins/marketplace::partials.header-actions", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fresources%2Fviews%2Fpartials%2Fheader-actions.blade.php&line=1", "ajax": false, "filename": "header-actions.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::button", "param_count": null, "params": [], "start": **********.975709, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/button.blade.phpa74ad8dfacd4f985eb3977517615ce25::button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}}, {"name": "__components::d9cf6f2adf46159095e24cd6ce9ab42b", "param_count": null, "params": [], "start": **********.976421, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/d9cf6f2adf46159095e24cd6ce9ab42b.blade.php__components::d9cf6f2adf46159095e24cd6ce9ab42b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fd9cf6f2adf46159095e24cd6ce9ab42b.blade.php&line=1", "ajax": false, "filename": "d9cf6f2adf46159095e24cd6ce9ab42b.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::button", "param_count": null, "params": [], "start": **********.976784, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/button.blade.phpa74ad8dfacd4f985eb3977517615ce25::button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}}, {"name": "__components::d25e365b684c7fa9ad5ec13cfb768fc1", "param_count": null, "params": [], "start": **********.977442, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/d25e365b684c7fa9ad5ec13cfb768fc1.blade.php__components::d25e365b684c7fa9ad5ec13cfb768fc1", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fd25e365b684c7fa9ad5ec13cfb768fc1.blade.php&line=1", "ajax": false, "filename": "d25e365b684c7fa9ad5ec13cfb768fc1.blade.php", "line": "?"}}]}, "queries": {"count": 31, "nb_statements": 31, "nb_visible_statements": 31, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.07416999999999999, "accumulated_duration_str": "74.17ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}], "start": **********.646322, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0, "width_percent": 0.782}, {"sql": "select * from `mp_stores` where `id` = '412' limit 1", "type": "query", "params": [], "bindings": ["412"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 61}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 961}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 42}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.651832, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0.782, "width_percent": 0.647}, {"sql": "select `lang_locale`, `lang_code`, `lang_name`, `lang_flag`, `lang_is_rtl` from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\Providers\\HookServiceProvider.php", "line": 105}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "platform/core/base/src/Http/Middleware/AdminLocaleMiddleware.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php", "line": 28}], "start": **********.658139, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 1.429, "width_percent": 0.701}, {"sql": "select count(*) as aggregate from `mp_stores` where `email` = '<EMAIL>.347' and `id` <> '412'", "type": "query", "params": [], "bindings": ["<EMAIL>.347", "412"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 1029}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 686}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 481}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 516}], "start": **********.70293, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "muhrak", "explain": null, "start_percent": 2.13, "width_percent": 0.58}, {"sql": "select count(*) as aggregate from `ec_customers` where `id` = '2'", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 984}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 955}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 686}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 481}], "start": **********.706052, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "muhrak", "explain": null, "start_percent": 2.71, "width_percent": 0.715}, {"sql": "select exists(select * from `countries`) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 561}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Forms/Concerns/HasLocationFields.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Forms\\Concerns\\HasLocationFields.php", "line": 29}, {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Forms/StoreForm.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Forms\\StoreForm.php", "line": 97}, {"index": 15, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 100}, {"index": 16, "namespace": null, "name": "vendor/botble/form-builder/src/FormBuilder.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\form-builder\\src\\FormBuilder.php", "line": 44}], "start": **********.7698739, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "EcommerceHelper.php:561", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 561}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FEcommerceHelper.php&line=561", "ajax": false, "filename": "EcommerceHelper.php", "line": "561"}, "connection": "muhrak", "explain": null, "start_percent": 3.425, "width_percent": 0.512}, {"sql": "select exists(select * from `states`) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 562}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Forms/Concerns/HasLocationFields.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Forms\\Concerns\\HasLocationFields.php", "line": 29}, {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Forms/StoreForm.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Forms\\StoreForm.php", "line": 97}, {"index": 15, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 100}, {"index": 16, "namespace": null, "name": "vendor/botble/form-builder/src/FormBuilder.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\form-builder\\src\\FormBuilder.php", "line": 44}], "start": **********.7717981, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "EcommerceHelper.php:562", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 562}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FEcommerceHelper.php&line=562", "ajax": false, "filename": "EcommerceHelper.php", "line": "562"}, "connection": "muhrak", "explain": null, "start_percent": 3.937, "width_percent": 0.418}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Botble\\\\Marketplace\\\\Models\\\\Store' and `meta_boxes`.`reference_id` = 412 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Botble\\Marketplace\\Models\\Store", 412], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/marketplace/src/Models/Store.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Models\\Store.php", "line": 217}, {"index": 24, "namespace": "view", "name": "plugins/marketplace::partials.extra-content", "file": "D:\\laragon\\www\\muhrak\\platform/plugins/marketplace/resources/views/partials/extra-content.blade.php", "line": 12}], "start": **********.7834458, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 4.355, "width_percent": 0.58}, {"sql": "select `name`, `id` from `ec_customers` where `is_vendor` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Forms/StoreForm.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Forms\\StoreForm.php", "line": 166}, {"index": 15, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 100}, {"index": 16, "namespace": null, "name": "vendor/botble/form-builder/src/FormBuilder.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\form-builder\\src\\FormBuilder.php", "line": 44}, {"index": 17, "namespace": null, "name": "platform/core/base/src/Forms/FormBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Forms\\FormBuilder.php", "line": 11}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 576}], "start": **********.839741, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "StoreForm.php:166", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Forms/StoreForm.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Forms\\StoreForm.php", "line": 166}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FForms%2FStoreForm.php&line=166", "ajax": false, "filename": "StoreForm.php", "line": "166"}, "connection": "muhrak", "explain": null, "start_percent": 4.935, "width_percent": 0.715}, {"sql": "select `id`, `name`, `parent_id` from `mp_categories` where (`status` = 'published')", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/marketplace/src/Repositories/Eloquent/CategoryRepository.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Repositories\\Eloquent\\CategoryRepository.php", "line": 126}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/helpers/helper.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\helpers\\helper.php", "line": 22}, {"index": 19, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 100}, {"index": 20, "namespace": null, "name": "vendor/botble/form-builder/src/FormBuilder.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\form-builder\\src\\FormBuilder.php", "line": 44}], "start": **********.878748, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 5.649, "width_percent": 0.553}, {"sql": "select * from `mp_categories_translations` where `mp_categories_translations`.`mp_categories_id` in (18, 19, 20, 21) and `mp_categories_translations`.`lang_code` = 'en_US'", "type": "query", "params": [], "bindings": ["en_US"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/plugins/marketplace/src/Repositories/Eloquent/CategoryRepository.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Repositories\\Eloquent\\CategoryRepository.php", "line": 126}, {"index": 23, "namespace": null, "name": "platform/plugins/marketplace/helpers/helper.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\helpers\\helper.php", "line": 22}, {"index": 25, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 100}], "start": **********.8819032, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 6.202, "width_percent": 0.418}, {"sql": "select `category_id` from `mp_categories` inner join `store_categories` on `mp_categories`.`id` = `store_categories`.`category_id` where `store_categories`.`store_id` = 412", "type": "query", "params": [], "bindings": [412], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Forms/StoreForm.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Forms\\StoreForm.php", "line": 218}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Conditionable/Traits/Conditionable.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Conditionable\\Traits\\Conditionable.php", "line": 34}, {"index": 19, "namespace": null, "name": "platform/plugins/marketplace/src/Forms/StoreForm.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Forms\\StoreForm.php", "line": 217}, {"index": 20, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 100}, {"index": 21, "namespace": null, "name": "vendor/botble/form-builder/src/FormBuilder.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\form-builder\\src\\FormBuilder.php", "line": 44}], "start": **********.894314, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "StoreForm.php:218", "source": {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Forms/StoreForm.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Forms\\StoreForm.php", "line": 218}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FForms%2FStoreForm.php&line=218", "ajax": false, "filename": "StoreForm.php", "line": "218"}, "connection": "muhrak", "explain": null, "start_percent": 6.62, "width_percent": 0.593}, {"sql": "select `mp_categories`.*, `store_categories`.`store_id` as `pivot_store_id`, `store_categories`.`category_id` as `pivot_category_id` from `mp_categories` inner join `store_categories` on `mp_categories`.`id` = `store_categories`.`category_id` where `store_categories`.`store_id` = 412", "type": "query", "params": [], "bindings": [412], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/botble/form-builder/src/Fields/FormField.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\form-builder\\src\\Fields\\FormField.php", "line": 521}, {"index": 24, "namespace": null, "name": "vendor/botble/form-builder/src/Fields/FormField.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\form-builder\\src\\Fields\\FormField.php", "line": 131}, {"index": 25, "namespace": null, "name": "vendor/botble/form-builder/src/Form.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\form-builder\\src\\Form.php", "line": 136}, {"index": 26, "namespace": null, "name": "vendor/botble/form-builder/src/Form.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\form-builder\\src\\Form.php", "line": 92}, {"index": 27, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 396}], "start": **********.898386, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "FormField.php:521", "source": {"index": 23, "namespace": null, "name": "vendor/botble/form-builder/src/Fields/FormField.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\form-builder\\src\\Fields\\FormField.php", "line": 521}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fvendor%2Fbotble%2Fform-builder%2Fsrc%2FFields%2FFormField.php&line=521", "ajax": false, "filename": "FormField.php", "line": "521"}, "connection": "muhrak", "explain": null, "start_percent": 7.213, "width_percent": 0.566}, {"sql": "update `mp_stores` set `phone` = '12345678', `country` = null, `state` = null, `city` = null, `customer_id` = '2', `status` = 'published', `tax_id` = null, `mp_stores`.`updated_at` = '2025-06-13 17:19:21' where `id` = 412", "type": "query", "params": [], "bindings": ["12345678", null, null, null, "2", {"value": "published", "label": "Approved"}, null, "2025-06-13 17:19:21", 412], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 468}, {"index": 17, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 464}, {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Http/Controllers/StoreController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Http\\Controllers\\StoreController.php", "line": 120}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.927667, "duration": 0.00496, "duration_str": "4.96ms", "memory": 0, "memory_str": null, "filename": "FormAbstract.php:468", "source": {"index": 14, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 468}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FForms%2FFormAbstract.php&line=468", "ajax": false, "filename": "FormAbstract.php", "line": "468"}, "connection": "muhrak", "explain": null, "start_percent": 7.779, "width_percent": 6.687}, {"sql": "select `name`, `id` from `ec_customers` where `is_vendor` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Forms/StoreForm.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Forms\\StoreForm.php", "line": 166}, {"index": 15, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 100}, {"index": 16, "namespace": null, "name": "vendor/botble/form-builder/src/Form.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\form-builder\\src\\Form.php", "line": 199}, {"index": 17, "namespace": null, "name": "vendor/botble/form-builder/src/Form.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\form-builder\\src\\Form.php", "line": 405}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 470}], "start": **********.9669352, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "StoreForm.php:166", "source": {"index": 14, "namespace": null, "name": "platform/plugins/marketplace/src/Forms/StoreForm.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Forms\\StoreForm.php", "line": 166}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FForms%2FStoreForm.php&line=166", "ajax": false, "filename": "StoreForm.php", "line": "166"}, "connection": "muhrak", "explain": null, "start_percent": 14.467, "width_percent": 0.62}, {"sql": "select `id`, `name`, `parent_id` from `mp_categories` where (`status` = 'published')", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/marketplace/src/Repositories/Eloquent/CategoryRepository.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Repositories\\Eloquent\\CategoryRepository.php", "line": 126}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/helpers/helper.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\helpers\\helper.php", "line": 22}, {"index": 19, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 100}, {"index": 20, "namespace": null, "name": "vendor/botble/form-builder/src/Form.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\form-builder\\src\\Form.php", "line": 199}], "start": **********.979942, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 15.087, "width_percent": 0.688}, {"sql": "select * from `mp_categories_translations` where `mp_categories_translations`.`mp_categories_id` in (18, 19, 20, 21) and `mp_categories_translations`.`lang_code` = 'en_US'", "type": "query", "params": [], "bindings": ["en_US"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/plugins/marketplace/src/Repositories/Eloquent/CategoryRepository.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Repositories\\Eloquent\\CategoryRepository.php", "line": 126}, {"index": 23, "namespace": null, "name": "platform/plugins/marketplace/helpers/helper.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\helpers\\helper.php", "line": 22}, {"index": 25, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 100}], "start": **********.98191, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 15.775, "width_percent": 0.539}, {"sql": "select `category_id` from `mp_categories` inner join `store_categories` on `mp_categories`.`id` = `store_categories`.`category_id` where `store_categories`.`store_id` = 412", "type": "query", "params": [], "bindings": [412], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Forms/StoreForm.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Forms\\StoreForm.php", "line": 218}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Conditionable/Traits/Conditionable.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Conditionable\\Traits\\Conditionable.php", "line": 34}, {"index": 19, "namespace": null, "name": "platform/plugins/marketplace/src/Forms/StoreForm.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Forms\\StoreForm.php", "line": 217}, {"index": 20, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 100}, {"index": 21, "namespace": null, "name": "vendor/botble/form-builder/src/Form.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\botble\\form-builder\\src\\Form.php", "line": 199}], "start": **********.986636, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "StoreForm.php:218", "source": {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Forms/StoreForm.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Forms\\StoreForm.php", "line": 218}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FForms%2FStoreForm.php&line=218", "ajax": false, "filename": "StoreForm.php", "line": "218"}, "connection": "muhrak", "explain": null, "start_percent": 16.314, "width_percent": 0.566}, {"sql": "select * from `meta_boxes` where (`meta_key` = 'seo_meta' and `reference_id` = 412 and `reference_type` = 'Botble\\\\Marketplace\\\\Models\\\\Store') limit 1", "type": "query", "params": [], "bindings": ["seo_meta", 412, "Botble\\Marketplace\\Models\\Store"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 147}, {"index": 20, "namespace": null, "name": "platform/packages/seo-helper/src/SeoHelper.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\seo-helper\\src\\SeoHelper.php", "line": 171}, {"index": 22, "namespace": null, "name": "platform/packages/seo-helper/src/Listeners/UpdatedContentListener.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\seo-helper\\src\\Listeners\\UpdatedContentListener.php", "line": 15}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 473}], "start": **********.042372, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 16.88, "width_percent": 0.701}, {"sql": "insert into `meta_boxes` (`meta_key`, `reference_id`, `reference_type`, `meta_value`, `updated_at`, `created_at`) values ('seo_meta', 412, 'Botble\\\\Marketplace\\\\Models\\\\Store', '[{\\\"index\\\":\\\"index\\\"}]', '2025-06-13 17:19:22', '2025-06-13 17:19:22')", "type": "query", "params": [], "bindings": ["seo_meta", 412, "Botble\\Marketplace\\Models\\Store", "[{\"index\":\"index\"}]", "2025-06-13 17:19:22", "2025-06-13 17:19:22"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 156}, {"index": 17, "namespace": null, "name": "platform/packages/seo-helper/src/SeoHelper.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\seo-helper\\src\\SeoHelper.php", "line": 171}, {"index": 19, "namespace": null, "name": "platform/packages/seo-helper/src/Listeners/UpdatedContentListener.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\seo-helper\\src\\Listeners\\UpdatedContentListener.php", "line": 15}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 473}, {"index": 25, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 520}], "start": **********.0459971, "duration": 0.00575, "duration_str": "5.75ms", "memory": 0, "memory_str": null, "filename": "MetaBox.php:156", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 156}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FSupports%2FMetaBox.php&line=156", "ajax": false, "filename": "MetaBox.php", "line": "156"}, "connection": "muhrak", "explain": null, "start_percent": 17.581, "width_percent": 7.752}, {"sql": "select * from `slugs` where (`reference_type` = 'Botble\\\\Marketplace\\\\Models\\\\Store' and `reference_id` = 412) limit 1", "type": "query", "params": [], "bindings": ["Botble\\Marketplace\\Models\\Store", 412], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/packages/slug/src/Listeners/UpdatedContentListener.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\slug\\src\\Listeners\\UpdatedContentListener.php", "line": 49}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 473}, {"index": 23, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 520}, {"index": 24, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 511}], "start": **********.0721128, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 25.334, "width_percent": 1.038}, {"sql": "insert into `slugs` (`key`, `reference_type`, `reference_id`, `prefix`, `updated_at`, `created_at`) values ('mentpack', 'Botble\\\\Marketplace\\\\Models\\\\Store', 412, 'stores', '2025-06-13 17:19:22', '2025-06-13 17:19:22')", "type": "query", "params": [], "bindings": ["mentpack", "Botble\\Marketplace\\Models\\Store", 412, "stores", "2025-06-13 17:19:22", "2025-06-13 17:19:22"], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "platform/packages/slug/src/Listeners/UpdatedContentListener.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\slug\\src\\Listeners\\UpdatedContentListener.php", "line": 62}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 473}, {"index": 24, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 520}, {"index": 25, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 511}, {"index": 26, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 464}], "start": **********.07584, "duration": 0.01286, "duration_str": "12.86ms", "memory": 0, "memory_str": null, "filename": "UpdatedContentListener.php:62", "source": {"index": 18, "namespace": null, "name": "platform/packages/slug/src/Listeners/UpdatedContentListener.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\slug\\src\\Listeners\\UpdatedContentListener.php", "line": 62}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fpackages%2Fslug%2Fsrc%2FListeners%2FUpdatedContentListener.php&line=62", "ajax": false, "filename": "UpdatedContentListener.php", "line": "62"}, "connection": "muhrak", "explain": null, "start_percent": 26.372, "width_percent": 17.339}, {"sql": "select `lang_id` from `languages` where `lang_is_default` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 938}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 889}, {"index": 20, "namespace": null, "name": "platform/plugins/language/src/Listeners/UpdatedContentListener.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\Listeners\\UpdatedContentListener.php", "line": 16}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 473}], "start": **********.137419, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 43.71, "width_percent": 0.688}, {"sql": "insert into `audit_histories` (`user_agent`, `ip_address`, `module`, `action`, `user_id`, `reference_user`, `reference_id`, `reference_name`, `type`, `created_at`, `updated_at`, `request`) values ('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '127.0.0.1', 'store', 'updated', 1, 1, 412, 'MENTPACK', 'primary', '2025-06-13 17:19:22', '2025-06-13 17:19:22', '{\\\"name\\\":\\\"MENTPACK\\\",\\\"model\\\":\\\"Botble\\\\\\\\Marketplace\\\\\\\\Models\\\\\\\\Store\\\",\\\"slug\\\":\\\"mentpack\\\",\\\"slug_id\\\":\\\"0\\\",\\\"is_slug_editable\\\":\\\"1\\\",\\\"email\\\":\\\"<EMAIL>.347\\\",\\\"phone\\\":\\\"12345678\\\",\\\"description\\\":\\\"Welcome to Mentpack, a global player in the packaging industry. Founded in 1996, we have over 25 years of experience in providing innovative and high-quality packaging solutions.\\\",\\\"content\\\":\\\"<p>We offer a wide range of products, including Stick Pack, Sachet Machines, and Vertical Form Fill Seal packaging machines. Our ability to cater to both entry-level and worldwide brands has positioned us as a preferred solution partner for packaging challenges.<\\\\/p><p>&nbsp;<\\\\/p>\\\",\\\"country\\\":null,\\\"state\\\":null,\\\"city\\\":null,\\\"address\\\":null,\\\"company\\\":null,\\\"tax_id\\\":null,\\\"social_links\\\":{\\\"facebook\\\":null,\\\"twitter\\\":null,\\\"instagram\\\":null,\\\"pinterest\\\":null,\\\"youtube\\\":null,\\\"linkedin\\\":null,\\\"messenger\\\":null,\\\"flickr\\\":null,\\\"tiktok\\\":null,\\\"skype\\\":null,\\\"snapchat\\\":null,\\\"tumblr\\\":null,\\\"whatsapp\\\":null,\\\"wechat\\\":null,\\\"vimeo\\\":null},\\\"botble-marketplace-tables-data-room-table_length\\\":\\\"10\\\",\\\"distributors\\\":null,\\\"seo_meta\\\":{\\\"seo_title\\\":null,\\\"seo_description\\\":null,\\\"index\\\":\\\"index\\\"},\\\"seo_meta_image\\\":null,\\\"submitter\\\":\\\"apply\\\",\\\"language\\\":\\\"en_US\\\",\\\"status\\\":\\\"published\\\",\\\"customer_id\\\":\\\"2\\\",\\\"logo\\\":\\\"lkt-alshash-2025-06-13-fy-53447-m.png\\\",\\\"logo_square\\\":null,\\\"cover_image\\\":\\\"mentpack-team.jpg\\\"}')", "type": "query", "params": [], "bindings": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "127.0.0.1", "store", "updated", 1, 1, 412, "MENTPACK", "primary", "2025-06-13 17:19:22", "2025-06-13 17:19:22", "{\"name\":\"MENTPACK\",\"model\":\"Botble\\\\Marketplace\\\\Models\\\\Store\",\"slug\":\"mentpack\",\"slug_id\":\"0\",\"is_slug_editable\":\"1\",\"email\":\"<EMAIL>.347\",\"phone\":\"12345678\",\"description\":\"Welcome to Mentpack, a global player in the packaging industry. Founded in 1996, we have over 25 years of experience in providing innovative and high-quality packaging solutions.\",\"content\":\"<p>We offer a wide range of products, including Stick Pack, Sachet Machines, and Vertical Form Fill Seal packaging machines. Our ability to cater to both entry-level and worldwide brands has positioned us as a preferred solution partner for packaging challenges.<\\/p><p>&nbsp;<\\/p>\",\"country\":null,\"state\":null,\"city\":null,\"address\":null,\"company\":null,\"tax_id\":null,\"social_links\":{\"facebook\":null,\"twitter\":null,\"instagram\":null,\"pinterest\":null,\"youtube\":null,\"linkedin\":null,\"messenger\":null,\"flickr\":null,\"tiktok\":null,\"skype\":null,\"snapchat\":null,\"tumblr\":null,\"whatsapp\":null,\"wechat\":null,\"vimeo\":null},\"botble-marketplace-tables-data-room-table_length\":\"10\",\"distributors\":null,\"seo_meta\":{\"seo_title\":null,\"seo_description\":null,\"index\":\"index\"},\"seo_meta_image\":null,\"submitter\":\"apply\",\"language\":\"en_US\",\"status\":\"published\",\"customer_id\":\"2\",\"logo\":\"lkt-alshash-2025-06-13-fy-53447-m.png\",\"logo_square\":null,\"cover_image\":\"mentpack-team.jpg\"}"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/audit-log/src/Listeners/AuditHandlerListener.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\audit-log\\src\\Listeners\\AuditHandlerListener.php", "line": 60}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 473}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 473}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 520}, {"index": 23, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 511}], "start": **********.1824038, "duration": 0.00393, "duration_str": "3.93ms", "memory": 0, "memory_str": null, "filename": "AuditHandlerListener.php:60", "source": {"index": 11, "namespace": null, "name": "platform/plugins/audit-log/src/Listeners/AuditHandlerListener.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\audit-log\\src\\Listeners\\AuditHandlerListener.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Faudit-log%2Fsrc%2FListeners%2FAuditHandlerListener.php&line=60", "ajax": false, "filename": "AuditHandlerListener.php", "line": "60"}, "connection": "muhrak", "explain": null, "start_percent": 44.398, "width_percent": 5.299}, {"sql": "select * from `meta_boxes` where (`meta_key` = 'seo_meta' and `reference_id` = 412 and `reference_type` = 'Botble\\\\Marketplace\\\\Models\\\\Store') limit 1", "type": "query", "params": [], "bindings": ["seo_meta", 412, "Botble\\Marketplace\\Models\\Store"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 147}, {"index": 20, "namespace": null, "name": "platform/packages/seo-helper/src/SeoHelper.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\seo-helper\\src\\SeoHelper.php", "line": 171}, {"index": 22, "namespace": null, "name": "platform/packages/seo-helper/src/Listeners/UpdatedContentListener.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\seo-helper\\src\\Listeners\\UpdatedContentListener.php", "line": 15}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 473}], "start": **********.269515, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 49.697, "width_percent": 0.984}, {"sql": "select * from `slugs` where (`reference_type` = 'Botble\\\\Marketplace\\\\Models\\\\Store' and `reference_id` = 412) limit 1", "type": "query", "params": [], "bindings": ["Botble\\Marketplace\\Models\\Store", 412], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/packages/slug/src/Listeners/UpdatedContentListener.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\slug\\src\\Listeners\\UpdatedContentListener.php", "line": 49}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 473}, {"index": 23, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 520}, {"index": 24, "namespace": null, "name": "platform/plugins/marketplace/src/Http/Controllers/StoreController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Http\\Controllers\\StoreController.php", "line": 121}], "start": **********.2741292, "duration": 0.00363, "duration_str": "3.63ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 50.681, "width_percent": 4.894}, {"sql": "insert into `audit_histories` (`user_agent`, `ip_address`, `module`, `action`, `user_id`, `reference_user`, `reference_id`, `reference_name`, `type`, `created_at`, `updated_at`, `request`) values ('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '127.0.0.1', 'store', 'updated', 1, 1, 412, 'MENTPACK', 'primary', '2025-06-13 17:19:22', '2025-06-13 17:19:22', '{\\\"name\\\":\\\"MENTPACK\\\",\\\"model\\\":\\\"Botble\\\\\\\\Marketplace\\\\\\\\Models\\\\\\\\Store\\\",\\\"slug\\\":\\\"mentpack\\\",\\\"slug_id\\\":\\\"0\\\",\\\"is_slug_editable\\\":\\\"1\\\",\\\"email\\\":\\\"<EMAIL>.347\\\",\\\"phone\\\":\\\"12345678\\\",\\\"description\\\":\\\"Welcome to Mentpack, a global player in the packaging industry. Founded in 1996, we have over 25 years of experience in providing innovative and high-quality packaging solutions.\\\",\\\"content\\\":\\\"<p>We offer a wide range of products, including Stick Pack, Sachet Machines, and Vertical Form Fill Seal packaging machines. Our ability to cater to both entry-level and worldwide brands has positioned us as a preferred solution partner for packaging challenges.<\\\\/p><p>&nbsp;<\\\\/p>\\\",\\\"country\\\":null,\\\"state\\\":null,\\\"city\\\":null,\\\"address\\\":null,\\\"company\\\":null,\\\"tax_id\\\":null,\\\"social_links\\\":{\\\"facebook\\\":null,\\\"twitter\\\":null,\\\"instagram\\\":null,\\\"pinterest\\\":null,\\\"youtube\\\":null,\\\"linkedin\\\":null,\\\"messenger\\\":null,\\\"flickr\\\":null,\\\"tiktok\\\":null,\\\"skype\\\":null,\\\"snapchat\\\":null,\\\"tumblr\\\":null,\\\"whatsapp\\\":null,\\\"wechat\\\":null,\\\"vimeo\\\":null},\\\"botble-marketplace-tables-data-room-table_length\\\":\\\"10\\\",\\\"distributors\\\":null,\\\"seo_meta\\\":{\\\"seo_title\\\":null,\\\"seo_description\\\":null,\\\"index\\\":\\\"index\\\"},\\\"seo_meta_image\\\":null,\\\"submitter\\\":\\\"apply\\\",\\\"language\\\":\\\"en_US\\\",\\\"status\\\":\\\"published\\\",\\\"customer_id\\\":\\\"2\\\",\\\"logo\\\":\\\"lkt-alshash-2025-06-13-fy-53447-m.png\\\",\\\"logo_square\\\":null,\\\"cover_image\\\":\\\"mentpack-team.jpg\\\"}')", "type": "query", "params": [], "bindings": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "127.0.0.1", "store", "updated", 1, 1, 412, "MENTPACK", "primary", "2025-06-13 17:19:22", "2025-06-13 17:19:22", "{\"name\":\"MENTPACK\",\"model\":\"Botble\\\\Marketplace\\\\Models\\\\Store\",\"slug\":\"mentpack\",\"slug_id\":\"0\",\"is_slug_editable\":\"1\",\"email\":\"<EMAIL>.347\",\"phone\":\"12345678\",\"description\":\"Welcome to Mentpack, a global player in the packaging industry. Founded in 1996, we have over 25 years of experience in providing innovative and high-quality packaging solutions.\",\"content\":\"<p>We offer a wide range of products, including Stick Pack, Sachet Machines, and Vertical Form Fill Seal packaging machines. Our ability to cater to both entry-level and worldwide brands has positioned us as a preferred solution partner for packaging challenges.<\\/p><p>&nbsp;<\\/p>\",\"country\":null,\"state\":null,\"city\":null,\"address\":null,\"company\":null,\"tax_id\":null,\"social_links\":{\"facebook\":null,\"twitter\":null,\"instagram\":null,\"pinterest\":null,\"youtube\":null,\"linkedin\":null,\"messenger\":null,\"flickr\":null,\"tiktok\":null,\"skype\":null,\"snapchat\":null,\"tumblr\":null,\"whatsapp\":null,\"wechat\":null,\"vimeo\":null},\"botble-marketplace-tables-data-room-table_length\":\"10\",\"distributors\":null,\"seo_meta\":{\"seo_title\":null,\"seo_description\":null,\"index\":\"index\"},\"seo_meta_image\":null,\"submitter\":\"apply\",\"language\":\"en_US\",\"status\":\"published\",\"customer_id\":\"2\",\"logo\":\"lkt-alshash-2025-06-13-fy-53447-m.png\",\"logo_square\":null,\"cover_image\":\"mentpack-team.jpg\"}"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/audit-log/src/Listeners/AuditHandlerListener.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\audit-log\\src\\Listeners\\AuditHandlerListener.php", "line": 60}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 473}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 473}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 520}, {"index": 23, "namespace": null, "name": "platform/plugins/marketplace/src/Http/Controllers/StoreController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Http\\Controllers\\StoreController.php", "line": 121}], "start": **********.282376, "duration": 0.02375, "duration_str": "23.75ms", "memory": 0, "memory_str": null, "filename": "AuditHandlerListener.php:60", "source": {"index": 11, "namespace": null, "name": "platform/plugins/audit-log/src/Listeners/AuditHandlerListener.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\audit-log\\src\\Listeners\\AuditHandlerListener.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Faudit-log%2Fsrc%2FListeners%2FAuditHandlerListener.php&line=60", "ajax": false, "filename": "AuditHandlerListener.php", "line": "60"}, "connection": "muhrak", "explain": null, "start_percent": 55.575, "width_percent": 32.021}, {"sql": "select * from `store_categories` where `store_categories`.`store_id` = 412", "type": "query", "params": [], "bindings": [412], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Services/CategoryService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Services\\CategoryService.php", "line": 15}, {"index": 16, "namespace": null, "name": "platform/plugins/marketplace/src/Http/Controllers/StoreController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Http\\Controllers\\StoreController.php", "line": 122}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.308669, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "CategoryService.php:15", "source": {"index": 15, "namespace": null, "name": "platform/plugins/marketplace/src/Services/CategoryService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Services\\CategoryService.php", "line": 15}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FServices%2FCategoryService.php&line=15", "ajax": false, "filename": "CategoryService.php", "line": "15"}, "connection": "muhrak", "explain": null, "start_percent": 87.596, "width_percent": 0.917}, {"sql": "delete from `mp_distributor_stores` where `mp_distributor_stores`.`store_id` = 412", "type": "query", "params": [], "bindings": [412], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Services/DistributorService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Services\\DistributorService.php", "line": 16}, {"index": 13, "namespace": null, "name": "platform/plugins/marketplace/src/Http/Controllers/StoreController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Http\\Controllers\\StoreController.php", "line": 123}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.311038, "duration": 0.0037, "duration_str": "3.7ms", "memory": 0, "memory_str": null, "filename": "DistributorService.php:16", "source": {"index": 12, "namespace": null, "name": "platform/plugins/marketplace/src/Services/DistributorService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Services\\DistributorService.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FServices%2FDistributorService.php&line=16", "ajax": false, "filename": "DistributorService.php", "line": "16"}, "connection": "muhrak", "explain": null, "start_percent": 88.513, "width_percent": 4.989}, {"sql": "select * from `meta_boxes` where (`meta_key` = 'social_links' and `reference_id` = 412 and `reference_type` = 'Botble\\\\Marketplace\\\\Models\\\\Store') limit 1", "type": "query", "params": [], "bindings": ["social_links", 412, "Botble\\Marketplace\\Models\\Store"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 147}, {"index": 20, "namespace": null, "name": "platform/plugins/marketplace/src/Http/Controllers/StoreController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Http\\Controllers\\StoreController.php", "line": 130}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.337554, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 93.501, "width_percent": 1.119}, {"sql": "insert into `meta_boxes` (`meta_key`, `reference_id`, `reference_type`, `meta_value`, `updated_at`, `created_at`) values ('social_links', 412, 'Bo<PERSON>ble\\\\Marketplace\\\\Models\\\\Store', '[[]]', '2025-06-13 17:19:22', '2025-06-13 17:19:22')", "type": "query", "params": [], "bindings": ["social_links", 412, "Botble\\Marketplace\\Models\\Store", "[[]]", "2025-06-13 17:19:22", "2025-06-13 17:19:22"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 156}, {"index": 17, "namespace": null, "name": "platform/plugins/marketplace/src/Http/Controllers/StoreController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Http\\Controllers\\StoreController.php", "line": 130}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.342926, "duration": 0.0039900000000000005, "duration_str": "3.99ms", "memory": 0, "memory_str": null, "filename": "MetaBox.php:156", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 156}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FSupports%2FMetaBox.php&line=156", "ajax": false, "filename": "MetaBox.php", "line": "156"}, "connection": "muhrak", "explain": null, "start_percent": 94.62, "width_percent": 5.38}]}, "models": {"data": {"Botble\\Marketplace\\Models\\Category": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "Botble\\Language\\Models\\Language": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "Botble\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Botble\\Marketplace\\Models\\Store": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FModels%2FStore.php&line=1", "ajax": false, "filename": "Store.php", "line": "?"}}, "Botble\\Base\\Models\\MetaBox": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FMetaBox.php&line=1", "ajax": false, "filename": "MetaBox.php", "line": "?"}}, "Botble\\Slug\\Models\\Slug": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fpackages%2Fslug%2Fsrc%2FModels%2FSlug.php&line=1", "ajax": false, "filename": "Slug.php", "line": "?"}}}, "count": 15, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "https://muhrak.gc/admin/marketplaces/stores/edit/412", "action_name": "marketplace.store.edit.update", "controller_action": "Botble\\Marketplace\\Http\\Controllers\\StoreController@update", "uri": "POST admin/marketplaces/stores/edit/{store}", "controller": "Botble\\Marketplace\\Http\\Controllers\\StoreController@update<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FHttp%2FControllers%2FStoreController.php&line=116\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Botble\\Marketplace\\Http\\Controllers", "prefix": "admin/marketplaces/stores", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FHttp%2FControllers%2FStoreController.php&line=116\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/plugins/marketplace/src/Http/Controllers/StoreController.php:116-138</a>", "middleware": "web, core, auth", "duration": "1.75s", "peak_memory": "56MB", "response": "Redirect to https://muhrak.gc/admin/marketplaces/stores/edit/412", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1532041192 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1532041192\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-57013862 data-indent-pad=\"  \"><span class=sf-dump-note>array:28</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FtY4EDLj9P6dViTp4GrWt6OFO4QWGtrBz9X4LzQ3</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">MENTPACK</span>\"\n  \"<span class=sf-dump-key>model</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Botble\\Marketplace\\Models\\Store</span>\"\n  \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"8 characters\">mentpack</span>\"\n  \"<span class=sf-dump-key>slug_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>is_slug_editable</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"21 characters\"><EMAIL>.347</span>\"\n  \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"8 characters\">12345678</span>\"\n  \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"178 characters\">Welcome to Mentpack, a global player in the packaging industry. Founded in 1996, we have over 25 years of experience in providing innovative and high-quality packaging solutions.</span>\"\n  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"279 characters\">&lt;p&gt;We offer a wide range of products, including Stick Pack, Sachet Machines, and Vertical Form Fill Seal packaging machines. Our ability to cater to both entry-level and worldwide brands has positioned us as a preferred solution partner for packaging challenges.&lt;/p&gt;&lt;p&gt;&amp;nbsp;&lt;/p&gt;</span>\"\n  \"<span class=sf-dump-key>country</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>state</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>city</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>address</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>company</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>tax_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>social_links</span>\" => <span class=sf-dump-note>array:15</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>facebook</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>twitter</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>instagram</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>pinterest</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>youtube</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>linkedin</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>messenger</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>flickr</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>tiktok</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>skype</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>snapchat</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>tumblr</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>whatsapp</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>wechat</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>vimeo</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>botble-marketplace-tables-data-room-table_length</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>distributors</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>seo_meta</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>seo_title</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>seo_description</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>index</span>\" => \"<span class=sf-dump-str title=\"5 characters\">index</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>seo_meta_image</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>submitter</span>\" => \"<span class=sf-dump-str title=\"5 characters\">apply</span>\"\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"5 characters\">en_US</span>\"\n  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">published</span>\"\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>2</span>\"\n  \"<span class=sf-dump-key>logo</span>\" => \"<span class=sf-dump-str title=\"37 characters\">lkt-alshash-2025-06-13-fy-53447-m.png</span>\"\n  \"<span class=sf-dump-key>logo_square</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cover_image</span>\" => \"<span class=sf-dump-str title=\"17 characters\">mentpack-team.jpg</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-57013862\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-582001717 data-indent-pad=\"  \"><span class=sf-dump-note>array:21</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">muhrak.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">5344</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">https://muhrak.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryHmCNHvO2dmIG6QNK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">https://muhrak.gc/admin/marketplaces/stores/edit/412</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3140 characters\">cookie_for_consent=1; botble_footprints_cookie=eyJpdiI6InJtenpwcG01ZldyRCs2TFV4YUYrYUE9PSIsInZhbHVlIjoiamVKQ2MwT2VxdDI3L0pXTXdlMURsY0JJSHdaWVp5T1VoSUNSbEpIZ0pZcHdKWGZId3lBLzJ2b1pIUXVYUUxKRjdMY3k5Q1VpTWRFd3Ftbzd6L3UyeHUvdWFpdTAwUXBNZER4MDc5MGJjOXQxQXhIazNiaTI0TU42YXJkbHNVek8iLCJtYWMiOiI3YWM1Njc0ODBhMzFhMjBlOWM2NzQ2YTE1ODRkNGM0ODk5YTNhYjE5ZjliNjY3NGI2MzA5MjMxNGJhNDliZjJjIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6ImozUjJ6OXN5RkhxbXdCYmc5K1JURlE9PSIsInZhbHVlIjoib3plTVp0NnFEZVhoU1NQejZBMklhY2RqSEtwTmNvaG1SeWtpbFkvZmVZb21QUmg4VTFvRVlPbUUzd1NGQ3J6dFZVT0hmdDIrTWd0SGF1Y28yYWx1VWJYdTJ0OUFvdkxJM3U3UktZNXpBL1pReU8xcDI0S3AxSmRkVzVHaHhyZHlia1ptb0wrTUhkNHRPbXhoY2pNL2JoZ2JXVGNTaHI2VjRIRW1HOUdiVHAwN2xxdkJLazdwZDFadCtJaTNlTk9HT2JmWms2Vnp5bGorWkxFd1dvaStjUHlEY1ZQR2tYOW5tREUxV25Vek1jbDFkenJlZENXR3VIaHM4aFdLaDRVNjViY21idXByU3ZQQVFCU2gxcVNnYW5zbERUamgxZjNUMVRIWEt4V0tUbGxFRDc5VzhDNkZRZ0g1QXRjU3Rna3ZTQlRwZjVScFM1eEdzUmNhSlAxaHpIZ2kxdVEvOG51K1FlVmhuSTBGdUZxYXdkdGdWLzFDdW1nZ0MvdDFnTit6STdZd3JJWnVCNGZZSTNFRWtjaTJndzZVT3prNEkrb1c0cTFFaGFWckxiUUtVSFVsYXlWSWxnUSttR2dxYlo0K0dCY1NzRDBGdzVNZGdLTnFOZG43VllRTkFORnIrZlk1SEt3ZFkvNjYwdjJFWmFJaCtPUVh4TjFZejFleUQvU09oeERvVkdyVVJ2d0ZkM25TZXU1OGNZOE5PWTJWVmV4WWpsWUE2Zmc0dHV3PSIsIm1hYyI6IjYwMTc0ODhiZDNjN2VkZGVmZjliNmYyNWU0ZTU2ZWE5YjhkMjllNzk1ZDAxNGU2ZWU3MDhkOTA3NzM3OTFjNjUiLCJ0YWciOiIifQ%3D%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IktsRlR4UnBrM0szS1Y0SUxrVVlUdHc9PSIsInZhbHVlIjoibVgzWjBpSHR4T2o4NlJBc3ZWNEVGZGVXS0xzU3NlNjRPOXFyNVFIOVVkbCt1ODRxbVZFNkNaODlSa3BORzF3bTJEb3Rkb0RVTVVjZHNBVFZiVnQ2TlhLOVBNVkJhVUIzb0ZZV2VTOER2dEJjd1ZZZEw3OWg4UEV4OXZqSjQwR2hsbERhOTdCQ1puTTJGRG1nMnozejdpcXp0NllsWUVzYWl5SjFlZGhYVWdUNmYyY2xRVXdXeG9UU3M3S1kxMnVxbVc5bktlTHFEOHNJbDlUV2JPamxrK3AwangreXlUMmtBTVhRdlN0SHQwcz0iLCJtYWMiOiIzNTliMzJjOTQxODc2NDdkNDI0YTBiNjZkMzUzZjEzNzZjYWMwYzlmNDQ2NGZhZjJiOTk5M2NhYTc0YmEyOGY5IiwidGFnIjoiIn0%3D; remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ijl6MUxxZXpoME96cm9sVGNGeUlyaEE9PSIsInZhbHVlIjoiWlR1dnBBRnpBZWF2c1I3dndid2M5UllhSUVPN0Fqa1p0K0RGelpiVkRpditYckx0YWZ2RVZFbTMwUGVnVGh4ODc5VFBseUtFaytWNWpRalB1QVhJbHZONFhqaFc2UjdjTlI1SlR0TDAwMS9ldmZxN1F5aTZpaWdnaFNOaGs0VmRWMlBoNkhMeTZKSU1hVTA3UHRKNUJKb3hPUEJVUWtEcjc2RFZmQ3NVanJEeHNHMU1jc3QzQ2N5blJKUUJVaFNsUEFQSmVKSHBiTjhtVmU2OEUwY1dBMTNyblVPOENzcTlRcHBOOTVuTWc1OD0iLCJtYWMiOiJhNzJhNzEyNGRjOTM0ZmVhMTIyNDU0ZmM0Mzc2MDQzZjI3MjNjNmM4NTUwMGQ2ZTZlNmZjM2NlMWZkYTgyMGM0IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InVIMnlLVkhBUU5vQndOZVVaTmVka2c9PSIsInZhbHVlIjoiLzRDWDB1T0JxRWxJV2lwQU56cDlhNWhoMGwzWGpwYk5pcFJrdTV3WC9LZFFVb2Q1ZWMvMEtRbUJsMkI0Rk13R1VHVTNkMnZHY21oUlRWVW5zZ0ZmYmNkN25KQTBKZ2NHejNyYVVpR0l4TFBUd0I0eXZ5NUdsR2NjdUhmVnhWRk4iLCJtYWMiOiI2NzZmOTM2OGQ1Y2ZlZWQxNTNkZTE2NmMxNTU1NjdiZDlhMTVhYTRjNDg3ODEyNTkzYTYzZDc2ZjJjZmY3YzFhIiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6Ii9vTGh5Sy9iSjB4VmRtcU1zVmFWSmc9PSIsInZhbHVlIjoibFMyTktIMmgzUHZnSk1INWY3RWtaVldzcEFIYnJHa3ZuQmlmUGNXd2xEMGFKaG9rMmtzZG5XWWNFSjJQaGdIL2xYdFYyWUxWWVROZDduWFBWU2JuaDdkYUxOUVJuY1BIV1ZIT3FCeU1rNm1LdzFJQTNNRUFobjBjWWoxUXNrSnUiLCJtYWMiOiI3MTczMTk1NzI2NDYzYWYyODRhNjg3MTE4ODU5Y2QzYmZkZjMwODdkM2Y1ZmQ0NGJiZjVjYTIwMmZkZTg5YmRiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-582001717\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">14908b951300852119d2c46221b4b11a7e2b8107</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"359 characters\">{&quot;footprint&quot;:&quot;14908b951300852119d2c46221b4b11a7e2b8107&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;muhrak.gc&quot;,&quot;landing_page&quot;:&quot;install\\/welcome&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|lEoydfVrZKIFerelBjYotnW4P2e0TuoqJSFCOOr89XMprC6ygbibdEjl3rvX|$2y$12$6oEMzkNhsgbeo4WDg72G9e2nWxuvMvozEUdkIQ0AfpRz3PJNQ7XzG</span>\"\n  \"<span class=sf-dump-key>remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">2|eZ0awTe8bfQLceQTos5gvZ7R55kW3IiLBR997mfYDDJcPndE2TcWxwpqf39P|$2y$12$EU5h35Jju9igRCRES6rqCuE42l30uwJ94rSesc56rboRI0xQqYy/G</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FtY4EDLj9P6dViTp4GrWt6OFO4QWGtrBz9X4LzQ3</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7jtlU2dXcBjwVHs9Eymy9yeCbICkdDlwWf2uJ6r5</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 13 Jun 2025 17:19:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">https://muhrak.gc/admin/marketplaces/stores/edit/412</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1899711184 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FtY4EDLj9P6dViTp4GrWt6OFO4QWGtrBz9X4LzQ3</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"43 characters\">https://muhrak.gc/admin/marketplaces/stores</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">success_msg</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>success_msg</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Updated successfully</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1899711184\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "https://muhrak.gc/admin/marketplaces/stores/edit/412", "action_name": "marketplace.store.edit.update", "controller_action": "Botble\\Marketplace\\Http\\Controllers\\StoreController@update"}, "badge": "302 Found"}}