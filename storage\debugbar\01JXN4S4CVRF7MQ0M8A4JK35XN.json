{"__meta": {"id": "01JXN4S4CVRF7MQ0M8A4JK35XN", "datetime": "2025-06-13 17:18:12", "utime": **********.380779, "method": "POST", "uri": "/admin/marketplaces/data-rooms/list/412?", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749835091.237931, "end": **********.380815, "duration": 1.1428840160369873, "duration_str": "1.14s", "measures": [{"label": "Booting", "start": 1749835091.237931, "relative_start": 0, "end": **********.239823, "relative_end": **********.239823, "duration": 1.**************, "duration_str": "1s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.239838, "relative_start": 1.****************, "end": **********.380819, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "141ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.263092, "relative_start": 1.****************, "end": **********.277449, "relative_end": **********.277449, "duration": 0.014356851577758789, "duration_str": "14.36ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: core/table::table-info", "start": **********.35058, "relative_start": 1.****************, "end": **********.35058, "relative_end": **********.35058, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0343a1b0800146d7d9cf6a9514ec7bf4", "start": **********.357957, "relative_start": 1.***************, "end": **********.357957, "relative_end": **********.357957, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::badge", "start": **********.359189, "relative_start": 1.121258020401001, "end": **********.359189, "relative_end": **********.359189, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.376449, "relative_start": 1.1385180950164795, "end": **********.376951, "relative_end": **********.376951, "duration": 0.0005018711090087891, "duration_str": "502μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 51157104, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.3.17", "Environment": "localhost", "Debug Mode": "Enabled", "URL": "muhrak.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 3, "nb_templates": 3, "templates": [{"name": "core/table::table-info", "param_count": null, "params": [], "start": **********.350535, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/table/resources/views/table-info.blade.phpcore/table::table-info", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Ftable-info.blade.php&line=1", "ajax": false, "filename": "table-info.blade.php", "line": "?"}}, {"name": "__components::0343a1b0800146d7d9cf6a9514ec7bf4", "param_count": null, "params": [], "start": **********.357919, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/0343a1b0800146d7d9cf6a9514ec7bf4.blade.php__components::0343a1b0800146d7d9cf6a9514ec7bf4", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F0343a1b0800146d7d9cf6a9514ec7bf4.blade.php&line=1", "ajax": false, "filename": "0343a1b0800146d7d9cf6a9514ec7bf4.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::badge", "param_count": null, "params": [], "start": **********.359152, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/badge.blade.phpa74ad8dfacd4f985eb3977517615ce25::badge", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fbadge.blade.php&line=1", "ajax": false, "filename": "badge.blade.php", "line": "?"}}]}, "queries": {"count": 4, "nb_statements": 4, "nb_visible_statements": 4, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0024999999999999996, "accumulated_duration_str": "2.5ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}], "start": **********.291681, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0, "width_percent": 18.8}, {"sql": "select `lang_locale`, `lang_code`, `lang_name`, `lang_flag`, `lang_is_rtl` from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\Providers\\HookServiceProvider.php", "line": 105}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "platform/core/base/src/Http/Middleware/AdminLocaleMiddleware.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php", "line": 28}], "start": **********.2993548, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 18.8, "width_percent": 16.8}, {"sql": "select count(*) as aggregate from (select `id`, `store_id`, `title`, `category`, `file`, `order`, `created_at` from `mp_data_rooms` where `store_id` = '412' order by `order` asc) count_row_table", "type": "query", "params": [], "bindings": ["412"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 165}, {"index": 16, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/DataTableAbstract.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\yajra\\laravel-datatables-oracle\\src\\DataTableAbstract.php", "line": 707}, {"index": 17, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 148}, {"index": 18, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 120}, {"index": 19, "namespace": null, "name": "platform/core/table/src/Abstracts/TableAbstract.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\table\\src\\Abstracts\\TableAbstract.php", "line": 918}], "start": **********.367307, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "QueryDataTable.php:165", "source": {"index": 15, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 165}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fvendor%2Fyajra%2Flaravel-datatables-oracle%2Fsrc%2FQueryDataTable.php&line=165", "ajax": false, "filename": "QueryDataTable.php", "line": "165"}, "connection": "muhrak", "explain": null, "start_percent": 35.6, "width_percent": 38.4}, {"sql": "select `id`, `store_id`, `title`, `category`, `file`, `order`, `created_at` from `mp_data_rooms` where `store_id` = '412' order by `order` asc, `id` desc limit 10 offset 0", "type": "query", "params": [], "bindings": ["412"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 137}, {"index": 17, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 120}, {"index": 18, "namespace": null, "name": "platform/core/table/src/Abstracts/TableAbstract.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\table\\src\\Abstracts\\TableAbstract.php", "line": 918}, {"index": 19, "namespace": null, "name": "platform/core/table/src/Abstracts/TableAbstract.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\table\\src\\Abstracts\\TableAbstract.php", "line": 333}], "start": **********.370821, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 74, "width_percent": 26}]}, "models": {"data": {"Botble\\Language\\Models\\Language": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "Botble\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://muhrak.gc/admin/marketplaces/data-rooms/list/412", "action_name": "marketplace.data-room.index", "controller_action": "Botble\\Marketplace\\Http\\Controllers\\DataRoomController@index", "uri": "GET admin/marketplaces/data-rooms/list/{id}", "controller": "Botble\\Marketplace\\Http\\Controllers\\DataRoomController@index<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FHttp%2FControllers%2FDataRoomController.php&line=24\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Botble\\Marketplace\\Http\\Controllers", "prefix": "admin/marketplaces/data-rooms", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FHttp%2FControllers%2FDataRoomController.php&line=24\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/plugins/marketplace/src/Http/Controllers/DataRoomController.php:24-29</a>", "middleware": "web, core, auth", "duration": "1.14s", "peak_memory": "50MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1415698721 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1415698721\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1044267436 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>draw</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>columns</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"8 characters\">checkbox</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">checkbox</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"5 characters\">title</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">title</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"8 characters\">category</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">category</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"4 characters\">file</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">file</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"11 characters\">row_actions</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">row_actions</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>column</span>\" => \"<span class=sf-dump-str>1</span>\"\n      \"<span class=sf-dump-key>dir</span>\" => \"<span class=sf-dump-str title=\"4 characters\">desc</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>start</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>length</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1044267436\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-650527742 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">muhrak.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1663</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">FtY4EDLj9P6dViTp4GrWt6OFO4QWGtrBz9X4LzQ3</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">https://muhrak.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">https://muhrak.gc/admin/marketplaces/stores/edit/412</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3140 characters\">cookie_for_consent=1; botble_footprints_cookie=eyJpdiI6InJtenpwcG01ZldyRCs2TFV4YUYrYUE9PSIsInZhbHVlIjoiamVKQ2MwT2VxdDI3L0pXTXdlMURsY0JJSHdaWVp5T1VoSUNSbEpIZ0pZcHdKWGZId3lBLzJ2b1pIUXVYUUxKRjdMY3k5Q1VpTWRFd3Ftbzd6L3UyeHUvdWFpdTAwUXBNZER4MDc5MGJjOXQxQXhIazNiaTI0TU42YXJkbHNVek8iLCJtYWMiOiI3YWM1Njc0ODBhMzFhMjBlOWM2NzQ2YTE1ODRkNGM0ODk5YTNhYjE5ZjliNjY3NGI2MzA5MjMxNGJhNDliZjJjIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6ImozUjJ6OXN5RkhxbXdCYmc5K1JURlE9PSIsInZhbHVlIjoib3plTVp0NnFEZVhoU1NQejZBMklhY2RqSEtwTmNvaG1SeWtpbFkvZmVZb21QUmg4VTFvRVlPbUUzd1NGQ3J6dFZVT0hmdDIrTWd0SGF1Y28yYWx1VWJYdTJ0OUFvdkxJM3U3UktZNXpBL1pReU8xcDI0S3AxSmRkVzVHaHhyZHlia1ptb0wrTUhkNHRPbXhoY2pNL2JoZ2JXVGNTaHI2VjRIRW1HOUdiVHAwN2xxdkJLazdwZDFadCtJaTNlTk9HT2JmWms2Vnp5bGorWkxFd1dvaStjUHlEY1ZQR2tYOW5tREUxV25Vek1jbDFkenJlZENXR3VIaHM4aFdLaDRVNjViY21idXByU3ZQQVFCU2gxcVNnYW5zbERUamgxZjNUMVRIWEt4V0tUbGxFRDc5VzhDNkZRZ0g1QXRjU3Rna3ZTQlRwZjVScFM1eEdzUmNhSlAxaHpIZ2kxdVEvOG51K1FlVmhuSTBGdUZxYXdkdGdWLzFDdW1nZ0MvdDFnTit6STdZd3JJWnVCNGZZSTNFRWtjaTJndzZVT3prNEkrb1c0cTFFaGFWckxiUUtVSFVsYXlWSWxnUSttR2dxYlo0K0dCY1NzRDBGdzVNZGdLTnFOZG43VllRTkFORnIrZlk1SEt3ZFkvNjYwdjJFWmFJaCtPUVh4TjFZejFleUQvU09oeERvVkdyVVJ2d0ZkM25TZXU1OGNZOE5PWTJWVmV4WWpsWUE2Zmc0dHV3PSIsIm1hYyI6IjYwMTc0ODhiZDNjN2VkZGVmZjliNmYyNWU0ZTU2ZWE5YjhkMjllNzk1ZDAxNGU2ZWU3MDhkOTA3NzM3OTFjNjUiLCJ0YWciOiIifQ%3D%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IktsRlR4UnBrM0szS1Y0SUxrVVlUdHc9PSIsInZhbHVlIjoibVgzWjBpSHR4T2o4NlJBc3ZWNEVGZGVXS0xzU3NlNjRPOXFyNVFIOVVkbCt1ODRxbVZFNkNaODlSa3BORzF3bTJEb3Rkb0RVTVVjZHNBVFZiVnQ2TlhLOVBNVkJhVUIzb0ZZV2VTOER2dEJjd1ZZZEw3OWg4UEV4OXZqSjQwR2hsbERhOTdCQ1puTTJGRG1nMnozejdpcXp0NllsWUVzYWl5SjFlZGhYVWdUNmYyY2xRVXdXeG9UU3M3S1kxMnVxbVc5bktlTHFEOHNJbDlUV2JPamxrK3AwangreXlUMmtBTVhRdlN0SHQwcz0iLCJtYWMiOiIzNTliMzJjOTQxODc2NDdkNDI0YTBiNjZkMzUzZjEzNzZjYWMwYzlmNDQ2NGZhZjJiOTk5M2NhYTc0YmEyOGY5IiwidGFnIjoiIn0%3D; remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ijl6MUxxZXpoME96cm9sVGNGeUlyaEE9PSIsInZhbHVlIjoiWlR1dnBBRnpBZWF2c1I3dndid2M5UllhSUVPN0Fqa1p0K0RGelpiVkRpditYckx0YWZ2RVZFbTMwUGVnVGh4ODc5VFBseUtFaytWNWpRalB1QVhJbHZONFhqaFc2UjdjTlI1SlR0TDAwMS9ldmZxN1F5aTZpaWdnaFNOaGs0VmRWMlBoNkhMeTZKSU1hVTA3UHRKNUJKb3hPUEJVUWtEcjc2RFZmQ3NVanJEeHNHMU1jc3QzQ2N5blJKUUJVaFNsUEFQSmVKSHBiTjhtVmU2OEUwY1dBMTNyblVPOENzcTlRcHBOOTVuTWc1OD0iLCJtYWMiOiJhNzJhNzEyNGRjOTM0ZmVhMTIyNDU0ZmM0Mzc2MDQzZjI3MjNjNmM4NTUwMGQ2ZTZlNmZjM2NlMWZkYTgyMGM0IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlNEYzI2Mk9QRG1pcTR0eHlwMERtanc9PSIsInZhbHVlIjoiSTh5dzBGSEVNVTcyMDBpNk1aMFl5MjJldnAwSWtMS3F6S3VqWVhRalBzNnlobnU1eXJ0WTJMRzlqY2l4eXhGWHJUNi9NYklBUVYwdzZnSXZxL3locDZMa2h1QXE3QU9CYmVkQUlTa2VjSHdGbDJDckpTUUxrTFJqTUkrSXplbEEiLCJtYWMiOiJlYjc4MzVlNGVlMDhkYTIyODdlY2U1Y2MxZDA0NmFiNTI1NTI2YTg2MTg3ODdjODMxNDM4MDkxNWU2Y2MzOGUyIiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6Ilo1NWlaV1dtTHFsSFRXZ2pyNGhqSUE9PSIsInZhbHVlIjoiYUEyM1ZvWm96ZG9id05LWGhaTTlrZ2p2RkNkenFEdk1idHRZOWMzVDduc2RZWHZ5U2FtcDNZM1NEdzI4dTU0MDZsbFNGYnV2Uy80YVIzZTltUmQ0Mk9adWZDTHIvdU84eHRlcVZhNTlIY01CbjlQSFljNGNnOEVoVFpiTXJialYiLCJtYWMiOiJmYjk0NGM0MTk3YmI4MzQzMmQzOTQyYTI3YzAyMjhkM2VkZWU3ZTQ4MzMwYmQyZGQwOWNhZGY1ZDQ2ZWMwODQ2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-650527742\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-209337537 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">14908b951300852119d2c46221b4b11a7e2b8107</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"359 characters\">{&quot;footprint&quot;:&quot;14908b951300852119d2c46221b4b11a7e2b8107&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;muhrak.gc&quot;,&quot;landing_page&quot;:&quot;install\\/welcome&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|lEoydfVrZKIFerelBjYotnW4P2e0TuoqJSFCOOr89XMprC6ygbibdEjl3rvX|$2y$12$6oEMzkNhsgbeo4WDg72G9e2nWxuvMvozEUdkIQ0AfpRz3PJNQ7XzG</span>\"\n  \"<span class=sf-dump-key>remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">2|eZ0awTe8bfQLceQTos5gvZ7R55kW3IiLBR997mfYDDJcPndE2TcWxwpqf39P|$2y$12$EU5h35Jju9igRCRES6rqCuE42l30uwJ94rSesc56rboRI0xQqYy/G</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FtY4EDLj9P6dViTp4GrWt6OFO4QWGtrBz9X4LzQ3</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7jtlU2dXcBjwVHs9Eymy9yeCbICkdDlwWf2uJ6r5</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-209337537\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-462106578 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 13 Jun 2025 17:18:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-462106578\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-760399339 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FtY4EDLj9P6dViTp4GrWt6OFO4QWGtrBz9X4LzQ3</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"52 characters\">https://muhrak.gc/admin/marketplaces/stores/edit/412</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-760399339\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://muhrak.gc/admin/marketplaces/data-rooms/list/412", "action_name": "marketplace.data-room.index", "controller_action": "Botble\\Marketplace\\Http\\Controllers\\DataRoomController@index"}, "badge": null}}