{"__meta": {"id": "01JXN4T3MG1WR6Z9Q4CHFFPYWF", "datetime": "2025-06-13 17:18:44", "utime": **********.369693, "method": "POST", "uri": "/admin/marketplaces/stores/edit/412", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749835123.49014, "end": **********.36971, "duration": 0.8795700073242188, "duration_str": "880ms", "measures": [{"label": "Booting", "start": 1749835123.49014, "relative_start": 0, "end": **********.223855, "relative_end": **********.223855, "duration": 0.****************, "duration_str": "734ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.223867, "relative_start": 0.***************, "end": **********.369712, "relative_end": 2.1457672119140625e-06, "duration": 0.****************, "duration_str": "146ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.286161, "relative_start": 0.****************, "end": **********.29624, "relative_end": **********.29624, "duration": 0.010079145431518555, "duration_str": "10.08ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "48MB"}, "exceptions": {"count": 1, "exceptions": [{"type": "Illuminate\\Validation\\ValidationException", "message": "The selected customer id is invalid.", "code": 0, "file": "platform/core/js-validation/src/Remote/Validator.php", "line": 140, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:90</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"52 characters\">platform/core/js-validation/src/Remote/Validator.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>34</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">throwValidationException</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Botble\\JsValidation\\Remote\\Validator</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">The selected customer id is invalid.</span>\"\n      </samp>]\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"40 characters\">[object Illuminate\\Validation\\Validator]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"51 characters\">platform/core/js-validation/src/Remote/Resolver.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>83</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">validate</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Botble\\JsValidation\\Remote\\Validator</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">customer_id</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"64 characters\">vendor/laravel/framework/src/Illuminate/Validation/Validator.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1640</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Botble\\JsValidation\\Remote\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Botble\\JsValidation\\Remote\\Resolver</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">_js_validation</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"11 characters\">customer_id</span>\"\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"40 characters\">[object Illuminate\\Validation\\Validator]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"64 characters\">vendor/laravel/framework/src/Illuminate/Validation/Validator.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1674</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"13 characters\">callExtension</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Illuminate\\Validation\\Validator</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">js_validation</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">_js_validation</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"11 characters\">customer_id</span>\"\n        <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n        </samp>]\n        <span class=sf-dump-index>3</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Validation\\Validator\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Validation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Validator</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref24358 title=\"2 occurrences\">#4358</a><samp data-depth=5 id=sf-dump-**********-ref24358 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">translator</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Translation\\Translator\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Translation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Translator</span></span> {<a class=sf-dump-ref>#1532</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">parsed</span>: <span class=sf-dump-note>array:45</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>plugins/captcha::captcha.admin_login_form</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">plugins/captcha</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"7 characters\">captcha</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">admin_login_form</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>plugins/captcha::captcha.admin_forgot_password_form</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">plugins/captcha</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"7 characters\">captcha</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"26 characters\">admin_forgot_password_form</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>plugins/captcha::captcha.admin_reset_password_form</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">plugins/captcha</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"7 characters\">captcha</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"25 characters\">admin_reset_password_form</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>packages/menu::menu.main_navigation</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">packages/menu</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">menu</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"15 characters\">main_navigation</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>packages/page::pages.pages</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">packages/page</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"5 characters\">pages</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"5 characters\">pages</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>plugins/blog::base.blog_posts</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">plugins/blog</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">base</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">blog_posts</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>plugins/blog::base.blog_categories</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">plugins/blog</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">base</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"15 characters\">blog_categories</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>plugins/blog::base.blog_tags</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">plugins/blog</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">base</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"9 characters\">blog_tags</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>plugins/ecommerce::products.products</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">plugins/ecommerce</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">products</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">products</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>plugins/ecommerce::brands.brands</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">plugins/ecommerce</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"6 characters\">brands</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"6 characters\">brands</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>plugins/ecommerce::product-categories.product_categories</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">plugins/ecommerce</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"18 characters\">product-categories</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"18 characters\">product_categories</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>plugins/ecommerce::product-tag.product_tags</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">plugins/ecommerce</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"11 characters\">product-tag</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"12 characters\">product_tags</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>plugins/ecommerce::product-model.product_models</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">plugins/ecommerce</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">product-model</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"14 characters\">product_models</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>plugins/marketplace::store.stores</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">plugins/marketplace</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"5 characters\">store</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"6 characters\">stores</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>plugins/marketplace::category.name</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">plugins/marketplace</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">category</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>packages/slug::slug.current_year</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">packages/slug</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"12 characters\">current_year</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>packages/slug::slug.current_month</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">packages/slug</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"13 characters\">current_month</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>packages/slug::slug.current_day</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">packages/slug</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"11 characters\">current_day</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>packages/widget::widget.primary_sidebar_name</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">packages/widget</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"6 characters\">widget</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"20 characters\">primary_sidebar_name</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>packages/widget::widget.primary_sidebar_description</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">packages/widget</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"6 characters\">widget</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"27 characters\">primary_sidebar_description</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>packages/widget::widget.widget_menu</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">packages/widget</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"6 characters\">widget</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"11 characters\">widget_menu</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>packages/widget::widget.widget_menu_description</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">packages/widget</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"6 characters\">widget</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"23 characters\">widget_menu_description</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>packages/widget::widget.widget_text</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">packages/widget</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"6 characters\">widget</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"11 characters\">widget_text</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>packages/widget::widget.widget_text_description</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">packages/widget</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"6 characters\">widget</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"23 characters\">widget_text_description</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>plugins/blog::base.short_code_name</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">plugins/blog</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">base</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"15 characters\">short_code_name</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>plugins/blog::base.short_code_description</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">plugins/blog</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">base</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"22 characters\">short_code_description</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>plugins/contact::contact.shortcode_name</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">plugins/contact</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"7 characters\">contact</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"14 characters\">shortcode_name</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>plugins/contact::contact.shortcode_description</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">plugins/contact</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"7 characters\">contact</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"21 characters\">shortcode_description</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>plugins/simple-slider::simple-slider.simple_slider_shortcode_name</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">plugins/simple-slider</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">simple-slider</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"28 characters\">simple_slider_shortcode_name</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>plugins/simple-slider::simple-slider.simple_slider_shortcode_description</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">plugins/simple-slider</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">simple-slider</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"35 characters\">simple_slider_shortcode_description</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>plugins/ads::ads.not_set</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">plugins/ads</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"3 characters\">ads</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"7 characters\">not_set</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>Hero Section</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=sf-dump-const>null</span>\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"12 characters\">Hero Section</span>\"\n                <span class=sf-dump-index>2</span> => <span class=sf-dump-const>null</span>\n              </samp>]\n              \"<span class=sf-dump-key>Benefits Section</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=sf-dump-const>null</span>\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">Benefits Section</span>\"\n                <span class=sf-dump-index>2</span> => <span class=sf-dump-const>null</span>\n              </samp>]\n              \"<span class=sf-dump-key>How To Buy Section</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=sf-dump-const>null</span>\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"18 characters\">How To Buy Section</span>\"\n                <span class=sf-dump-index>2</span> => <span class=sf-dump-const>null</span>\n              </samp>]\n              \"<span class=sf-dump-key>core/base::base.yes</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">core/base</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">base</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"3 characters\">yes</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>core/base::base.no</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">core/base</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">base</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"2 characters\">no</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>White logo</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=sf-dump-const>null</span>\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"10 characters\">White logo</span>\"\n                <span class=sf-dump-index>2</span> => <span class=sf-dump-const>null</span>\n              </samp>]\n              \"<span class=sf-dump-key>plugins/newsletter::newsletter.newsletter_form</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">plugins/newsletter</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"10 characters\">newsletter</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"15 characters\">newsletter_form</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>plugins/contact::contact.contact_form</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">plugins/contact</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"7 characters\">contact</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"12 characters\">contact_form</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>plugins/marketplace::marketplace.contact_store.form_name</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">plugins/marketplace</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"11 characters\">marketplace</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"23 characters\">contact_store.form_name</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>validation.custom.customer_id.exists</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=sf-dump-const>null</span>\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"10 characters\">validation</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"25 characters\">custom.customer_id.exists</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>validation.custom</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=sf-dump-const>null</span>\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"10 characters\">validation</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"6 characters\">custom</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>validation.exists</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=sf-dump-const>null</span>\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"10 characters\">validation</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"6 characters\">exists</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>validation.attributes</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=sf-dump-const>null</span>\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"10 characters\">validation</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">attributes</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>validation.values.customer_id.0</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=sf-dump-const>null</span>\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"10 characters\">validation</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"20 characters\">values.customer_id.0</span>\"\n              </samp>]\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">loader</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Translation\\FileLoader\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Translation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">FileLoader</span></span> {<a class=sf-dump-ref>#1531</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">files</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Filesystem\\Filesystem\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Filesystem</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Filesystem</span></span> {<a class=sf-dump-ref>#173</a>}\n              #<span class=sf-dump-protected title=\"Protected property\">paths</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"78 characters\">D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Translation/lang</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"26 characters\">D:\\laragon\\www\\muhrak\\lang</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">jsonPaths</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"49 characters\">D:\\laragon\\www\\muhrak\\platform\\themes\\muhrak/lang</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"47 characters\">D:\\laragon\\www\\muhrak\\lang\\vendor/themes/muhrak</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">hints</span>: <span class=sf-dump-note>array:41</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>packages/api</span>\" => \"<span class=sf-dump-str title=\"74 characters\">D:\\laragon\\www\\muhrak\\vendor\\botble\\api\\src\\Providers/../../resources/lang</span>\"\n                \"<span class=sf-dump-key>packages/data-synchronize</span>\" => \"<span class=sf-dump-str title=\"87 characters\">D:\\laragon\\www\\muhrak\\vendor\\botble\\data-synchronize\\src\\Providers/../../resources/lang</span>\"\n                \"<span class=sf-dump-key>packages/get-started</span>\" => \"<span class=sf-dump-str title=\"66 characters\">D:\\laragon\\www\\muhrak\\platform/packages/get-started/resources/lang</span>\"\n                \"<span class=sf-dump-key>packages/installer</span>\" => \"<span class=sf-dump-str title=\"64 characters\">D:\\laragon\\www\\muhrak\\platform/packages/installer/resources/lang</span>\"\n                \"<span class=sf-dump-key>packages/menu</span>\" => \"<span class=sf-dump-str title=\"59 characters\">D:\\laragon\\www\\muhrak\\platform/packages/menu/resources/lang</span>\"\n                \"<span class=sf-dump-key>packages/optimize</span>\" => \"<span class=sf-dump-str title=\"63 characters\">D:\\laragon\\www\\muhrak\\platform/packages/optimize/resources/lang</span>\"\n                \"<span class=sf-dump-key>packages/page</span>\" => \"<span class=sf-dump-str title=\"59 characters\">D:\\laragon\\www\\muhrak\\platform/packages/page/resources/lang</span>\"\n                \"<span class=sf-dump-key>core/setting</span>\" => \"<span class=sf-dump-str title=\"58 characters\">D:\\laragon\\www\\muhrak\\platform/core/setting/resources/lang</span>\"\n                \"<span class=sf-dump-key>core/base</span>\" => \"<span class=sf-dump-str title=\"55 characters\">D:\\laragon\\www\\muhrak\\platform/core/base/resources/lang</span>\"\n                \"<span class=sf-dump-key>core/table</span>\" => \"<span class=sf-dump-str title=\"56 characters\">D:\\laragon\\www\\muhrak\\platform/core/table/resources/lang</span>\"\n                \"<span class=sf-dump-key>core/acl</span>\" => \"<span class=sf-dump-str title=\"54 characters\">D:\\laragon\\www\\muhrak\\platform/core/acl/resources/lang</span>\"\n                \"<span class=sf-dump-key>core/dashboard</span>\" => \"<span class=sf-dump-str title=\"60 characters\">D:\\laragon\\www\\muhrak\\platform/core/dashboard/resources/lang</span>\"\n                \"<span class=sf-dump-key>core/media</span>\" => \"<span class=sf-dump-str title=\"56 characters\">D:\\laragon\\www\\muhrak\\platform/core/media/resources/lang</span>\"\n                \"<span class=sf-dump-key>packages/plugin-management</span>\" => \"<span class=sf-dump-str title=\"72 characters\">D:\\laragon\\www\\muhrak\\platform/packages/plugin-management/resources/lang</span>\"\n                \"<span class=sf-dump-key>packages/seo-helper</span>\" => \"<span class=sf-dump-str title=\"65 characters\">D:\\laragon\\www\\muhrak\\platform/packages/seo-helper/resources/lang</span>\"\n                \"<span class=sf-dump-key>packages/shortcode</span>\" => \"<span class=sf-dump-str title=\"64 characters\">D:\\laragon\\www\\muhrak\\platform/packages/shortcode/resources/lang</span>\"\n                \"<span class=sf-dump-key>packages/slug</span>\" => \"<span class=sf-dump-str title=\"59 characters\">D:\\laragon\\www\\muhrak\\platform/packages/slug/resources/lang</span>\"\n                \"<span class=sf-dump-key>packages/theme</span>\" => \"<span class=sf-dump-str title=\"60 characters\">D:\\laragon\\www\\muhrak\\platform/packages/theme/resources/lang</span>\"\n                \"<span class=sf-dump-key>packages/widget</span>\" => \"<span class=sf-dump-str title=\"61 characters\">D:\\laragon\\www\\muhrak\\platform/packages/widget/resources/lang</span>\"\n                \"<span class=sf-dump-key>plugins/language</span>\" => \"<span class=sf-dump-str title=\"62 characters\">D:\\laragon\\www\\muhrak\\platform/plugins/language/resources/lang</span>\"\n                \"<span class=sf-dump-key>plugins/ads</span>\" => \"<span class=sf-dump-str title=\"57 characters\">D:\\laragon\\www\\muhrak\\platform/plugins/ads/resources/lang</span>\"\n                \"<span class=sf-dump-key>plugins/analytics</span>\" => \"<span class=sf-dump-str title=\"63 characters\">D:\\laragon\\www\\muhrak\\platform/plugins/analytics/resources/lang</span>\"\n                \"<span class=sf-dump-key>plugins/audit-log</span>\" => \"<span class=sf-dump-str title=\"63 characters\">D:\\laragon\\www\\muhrak\\platform/plugins/audit-log/resources/lang</span>\"\n                \"<span class=sf-dump-key>plugins/backup</span>\" => \"<span class=sf-dump-str title=\"60 characters\">D:\\laragon\\www\\muhrak\\platform/plugins/backup/resources/lang</span>\"\n                \"<span class=sf-dump-key>plugins/blog</span>\" => \"<span class=sf-dump-str title=\"58 characters\">D:\\laragon\\www\\muhrak\\platform/plugins/blog/resources/lang</span>\"\n                \"<span class=sf-dump-key>plugins/captcha</span>\" => \"<span class=sf-dump-str title=\"61 characters\">D:\\laragon\\www\\muhrak\\platform/plugins/captcha/resources/lang</span>\"\n                \"<span class=sf-dump-key>plugins/contact</span>\" => \"<span class=sf-dump-str title=\"61 characters\">D:\\laragon\\www\\muhrak\\platform/plugins/contact/resources/lang</span>\"\n                \"<span class=sf-dump-key>plugins/cookie-consent</span>\" => \"<span class=sf-dump-str title=\"68 characters\">D:\\laragon\\www\\muhrak\\platform/plugins/cookie-consent/resources/lang</span>\"\n                \"<span class=sf-dump-key>plugins/ecommerce</span>\" => \"<span class=sf-dump-str title=\"63 characters\">D:\\laragon\\www\\muhrak\\platform/plugins/ecommerce/resources/lang</span>\"\n                \"<span class=sf-dump-key>plugins/faq</span>\" => \"<span class=sf-dump-str title=\"57 characters\">D:\\laragon\\www\\muhrak\\platform/plugins/faq/resources/lang</span>\"\n                \"<span class=sf-dump-key>plugins/location</span>\" => \"<span class=sf-dump-str title=\"62 characters\">D:\\laragon\\www\\muhrak\\platform/plugins/location/resources/lang</span>\"\n                \"<span class=sf-dump-key>plugins/newsletter</span>\" => \"<span class=sf-dump-str title=\"64 characters\">D:\\laragon\\www\\muhrak\\platform/plugins/newsletter/resources/lang</span>\"\n                \"<span class=sf-dump-key>plugins/payment</span>\" => \"<span class=sf-dump-str title=\"61 characters\">D:\\laragon\\www\\muhrak\\platform/plugins/payment/resources/lang</span>\"\n                \"<span class=sf-dump-key>plugins/shippo</span>\" => \"<span class=sf-dump-str title=\"60 characters\">D:\\laragon\\www\\muhrak\\platform/plugins/shippo/resources/lang</span>\"\n                \"<span class=sf-dump-key>plugins/simple-slider</span>\" => \"<span class=sf-dump-str title=\"67 characters\">D:\\laragon\\www\\muhrak\\platform/plugins/simple-slider/resources/lang</span>\"\n                \"<span class=sf-dump-key>plugins/social-login</span>\" => \"<span class=sf-dump-str title=\"66 characters\">D:\\laragon\\www\\muhrak\\platform/plugins/social-login/resources/lang</span>\"\n                \"<span class=sf-dump-key>plugins/stripe</span>\" => \"<span class=sf-dump-str title=\"60 characters\">D:\\laragon\\www\\muhrak\\platform/plugins/stripe/resources/lang</span>\"\n                \"<span class=sf-dump-key>plugins/stripe-connect</span>\" => \"<span class=sf-dump-str title=\"68 characters\">D:\\laragon\\www\\muhrak\\platform/plugins/stripe-connect/resources/lang</span>\"\n                \"<span class=sf-dump-key>plugins/translation</span>\" => \"<span class=sf-dump-str title=\"65 characters\">D:\\laragon\\www\\muhrak\\platform/plugins/translation/resources/lang</span>\"\n                \"<span class=sf-dump-key>plugins/marketplace</span>\" => \"<span class=sf-dump-str title=\"65 characters\">D:\\laragon\\www\\muhrak\\platform/plugins/marketplace/resources/lang</span>\"\n                \"<span class=sf-dump-key>plugins/fob-honeypot</span>\" => \"<span class=sf-dump-str title=\"66 characters\">D:\\laragon\\www\\muhrak\\platform/plugins/fob-honeypot/resources/lang</span>\"\n              </samp>]\n            </samp>}\n            #<span class=sf-dump-protected title=\"Protected property\">locale</span>: \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n            #<span class=sf-dump-protected title=\"Protected property\">fallback</span>: \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n            #<span class=sf-dump-protected title=\"Protected property\">loaded</span>: <span class=sf-dump-note>array:14</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>*</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>*</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>en</span>\" => <span class=sf-dump-note>array:1036</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>(:count reviews)</span>\" => \"<span class=sf-dump-str title=\"16 characters\">(:count reviews)</span>\"\n                    \"<span class=sf-dump-key>(:count)</span>\" => \"<span class=sf-dump-str title=\"8 characters\">(:count)</span>\"\n                    \"<span class=sf-dump-key>(Shipping fees not included)</span>\" => \"<span class=sf-dump-str title=\"28 characters\">(Shipping fees not included)</span>\"\n                    \"<span class=sf-dump-key>- (dash)</span>\" => \"<span class=sf-dump-str title=\"8 characters\">- (dash)</span>\"\n                    \"<span class=sf-dump-key>-- None --</span>\" => \"<span class=sf-dump-str title=\"10 characters\">-- None --</span>\"\n                    \"<span class=sf-dump-key>-- Select --</span>\" => \"<span class=sf-dump-str title=\"12 characters\">-- Select --</span>\"\n                    \"<span class=sf-dump-key>-- select --</span>\" => \"<span class=sf-dump-str title=\"12 characters\">-- select --</span>\"\n                    \"<span class=sf-dump-key>1 Review</span>\" => \"<span class=sf-dump-str title=\"8 characters\">1 Review</span>\"\n                    \"<span class=sf-dump-key>404 - Not found</span>\" => \"<span class=sf-dump-str title=\"15 characters\">404 - Not found</span>\"\n                    \"<span class=sf-dump-key>404 Page Not Found</span>\" => \"<span class=sf-dump-str title=\"18 characters\">404 Page Not Found</span>\"\n                    \"<span class=sf-dump-key>404 page image</span>\" => \"<span class=sf-dump-str title=\"14 characters\">404 page image</span>\"\n                    \"<span class=sf-dump-key>500 Internal Server Error</span>\" => \"<span class=sf-dump-str title=\"25 characters\">500 Internal Server Error</span>\"\n                    \"<span class=sf-dump-key>503 Service Unavailable</span>\" => \"<span class=sf-dump-str title=\"23 characters\">503 Service Unavailable</span>\"\n                    \"<span class=sf-dump-key>:count Reviews</span>\" => \"<span class=sf-dump-str title=\"14 characters\">:count Reviews</span>\"\n                    \"<span class=sf-dump-key>:count decrease</span>\" => \"<span class=sf-dump-str title=\"15 characters\">:count decrease</span>\"\n                    \"<span class=sf-dump-key>:count increase</span>\" => \"<span class=sf-dump-str title=\"15 characters\">:count increase</span>\"\n                    \"<span class=sf-dump-key>:count more</span>\" => \"<span class=sf-dump-str title=\"11 characters\">:count more</span>\"\n                    \"<span class=sf-dump-key>:customer has requested return product(s)</span>\" => \"<span class=sf-dump-str title=\"41 characters\">:customer has requested return product(s)</span>\"\n                    \"<span class=sf-dump-key>:name doesn&#039;t support :currency. List of currencies supported by :name: :currencies.</span>\" => \"<span class=sf-dump-str title=\"84 characters\">:name doesn&#039;t support :currency. List of currencies supported by :name: :currencies.</span>\"\n                    \"<span class=sf-dump-key>:name font family</span>\" => \"<span class=sf-dump-str title=\"17 characters\">:name font family</span>\"\n                    \"<span class=sf-dump-key>:name font size</span>\" => \"<span class=sf-dump-str title=\"15 characters\">:name font size</span>\"\n                    \"<span class=sf-dump-key>:number Star</span>\" => \"<span class=sf-dump-str title=\"12 characters\">:number Star</span>\"\n                    \"<span class=sf-dump-key>:number Stars</span>\" => \"<span class=sf-dump-str title=\"13 characters\">:number Stars</span>\"\n                    \"<span class=sf-dump-key>:number product available</span>\" => \"<span class=sf-dump-str title=\"25 characters\">:number product available</span>\"\n                    \"<span class=sf-dump-key>:number products available</span>\" => \"<span class=sf-dump-str title=\"26 characters\">:number products available</span>\"\n                    \"<span class=sf-dump-key>:price for :total item(s)</span>\" => \"<span class=sf-dump-str title=\"25 characters\">:price for :total item(s)</span>\"\n                    \"<span class=sf-dump-key>:product is already in your compare list!</span>\" => \"<span class=sf-dump-str title=\"41 characters\">:product is already in your compare list!</span>\"\n                    \"<span class=sf-dump-key>:total Product found</span>\" => \"<span class=sf-dump-str title=\"20 characters\">:total Product found</span>\"\n                    \"<span class=sf-dump-key>:total Products found</span>\" => \"<span class=sf-dump-str title=\"21 characters\">:total Products found</span>\"\n                    \"<span class=sf-dump-key>:total review(s) &quot;:star star&quot; for &quot;:product&quot;</span>\" => \"<span class=sf-dump-str title=\"44 characters\">:total review(s) &quot;:star star&quot; for &quot;:product&quot;</span>\"\n                    \"<span class=sf-dump-key>:total review(s) for &quot;:product&quot;</span>\" => \"<span class=sf-dump-str title=\"31 characters\">:total review(s) for &quot;:product&quot;</span>\"\n                    \"<span class=sf-dump-key>A new version (:version / released on :date) is available to update!</span>\" => \"<span class=sf-dump-str title=\"68 characters\">A new version (:version / released on :date) is available to update!</span>\"\n                    \"<span class=sf-dump-key>API Key</span>\" => \"<span class=sf-dump-str title=\"7 characters\">API Key</span>\"\n                    \"<span class=sf-dump-key>Accept and install</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Accept and install</span>\"\n                    \"<span class=sf-dump-key>Accepted Payment methods</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Accepted Payment methods</span>\"\n                    \"<span class=sf-dump-key>Accepted Payment methods link (optional)</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Accepted Payment methods link (optional)</span>\"\n                    \"<span class=sf-dump-key>Account</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Account</span>\"\n                    \"<span class=sf-dump-key>Account Holder Name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Account Holder Name</span>\"\n                    \"<span class=sf-dump-key>Account Number</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Account Number</span>\"\n                    \"<span class=sf-dump-key>Account Settings</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Account Settings</span>\"\n                    \"<span class=sf-dump-key>Account information</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Account information</span>\"\n                    \"<span class=sf-dump-key>Action</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Action</span>\"\n                    \"<span class=sf-dump-key>Actions</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Actions</span>\"\n                    \"<span class=sf-dump-key>Ad :number</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Ad :number</span>\"\n                    \"<span class=sf-dump-key>Add Google Maps iframe</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Add Google Maps iframe</span>\"\n                    \"<span class=sf-dump-key>Add To Cart</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Add To Cart</span>\"\n                    \"<span class=sf-dump-key>Add Wishlist</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Add Wishlist</span>\"\n                    \"<span class=sf-dump-key>Add YouTube video</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Add YouTube video</span>\"\n                    \"<span class=sf-dump-key>Add a custom menu to your widget area.</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Add a custom menu to your widget area.</span>\"\n                    \"<span class=sf-dump-key>Add a new address</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Add a new address</span>\"\n                    \"<span class=sf-dump-key>Add custom HTML content</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Add custom HTML content</span>\"\n                    \"<span class=sf-dump-key>Add new</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Add new</span>\"\n                    \"<span class=sf-dump-key>Add new address...</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Add new address...</span>\"\n                    \"<span class=sf-dump-key>Add to Cart</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Add to Cart</span>\"\n                    \"<span class=sf-dump-key>Add to Wishlist</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Add to Wishlist</span>\"\n                    \"<span class=sf-dump-key>Add to cart</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Add to cart</span>\"\n                    \"<span class=sf-dump-key>Add your review</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Add your review</span>\"\n                    \"<span class=sf-dump-key>Added product :product successfully!</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Added product :product successfully!</span>\"\n                    \"<span class=sf-dump-key>Added product :product to cart successfully!</span>\" => \"<span class=sf-dump-str title=\"44 characters\">Added product :product to cart successfully!</span>\"\n                    \"<span class=sf-dump-key>Added product :product to compare list successfully!</span>\" => \"<span class=sf-dump-str title=\"52 characters\">Added product :product to compare list successfully!</span>\"\n                    \"<span class=sf-dump-key>Added review successfully!</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Added review successfully!</span>\"\n                    \"<span class=sf-dump-key>Address</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Address</span>\"\n                    \"<span class=sf-dump-key>Address appears to be incomplete</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Address appears to be incomplete</span>\"\n                    \"<span class=sf-dump-key>Address books</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Address books</span>\"\n                    \"<span class=sf-dump-key>Addresses</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Addresses</span>\"\n                    \"<span class=sf-dump-key>Admin</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Admin</span>\"\n                    \"<span class=sf-dump-key>Ads</span>\" => \"<span class=sf-dump-str title=\"3 characters\">Ads</span>\"\n                    \"<span class=sf-dump-key>After cancel amount and fee will be refunded back in your balance</span>\" => \"<span class=sf-dump-str title=\"65 characters\">After cancel amount and fee will be refunded back in your balance</span>\"\n                    \"<span class=sf-dump-key>After registration at :name, you will have API key</span>\" => \"<span class=sf-dump-str title=\"50 characters\">After registration at :name, you will have API key</span>\"\n                    \"<span class=sf-dump-key>After registration at :name, you will have Client ID, Client Secret</span>\" => \"<span class=sf-dump-str title=\"67 characters\">After registration at :name, you will have Client ID, Client Secret</span>\"\n                    \"<span class=sf-dump-key>After registration at :name, you will have Public &amp; Secret keys</span>\" => \"<span class=sf-dump-str title=\"63 characters\">After registration at :name, you will have Public &amp; Secret keys</span>\"\n                    \"<span class=sf-dump-key>After registration at :name, you will have Store ID and Store Password (API/Secret key)</span>\" => \"<span class=sf-dump-str title=\"87 characters\">After registration at :name, you will have Store ID and Store Password (API/Secret key)</span>\"\n                    \"<span class=sf-dump-key>After the first image</span>\" => \"<span class=sf-dump-str title=\"21 characters\">After the first image</span>\"\n                    \"<span class=sf-dump-key>All</span>\" => \"<span class=sf-dump-str title=\"3 characters\">All</span>\"\n                    \"<span class=sf-dump-key>All Categories</span>\" => \"<span class=sf-dump-str title=\"14 characters\">All Categories</span>\"\n                    \"<span class=sf-dump-key>All Pages</span>\" => \"<span class=sf-dump-str title=\"9 characters\">All Pages</span>\"\n                    \"<span class=sf-dump-key>All categories</span>\" => \"<span class=sf-dump-str title=\"14 characters\">All categories</span>\"\n                    \"<span class=sf-dump-key>All files</span>\" => \"<span class=sf-dump-str title=\"9 characters\">All files</span>\"\n                    \"<span class=sf-dump-key>All messages are recorded and spam is not tolerated. Your email address will be shown to the recipient.</span>\" => \"<span class=sf-dump-str title=\"103 characters\">All messages are recorded and spam is not tolerated. Your email address will be shown to the recipient.</span>\"\n                    \"<span class=sf-dump-key>Already have an account?</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Already have an account?</span>\"\n                    \"<span class=sf-dump-key>Amount</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Amount</span>\"\n                    \"<span class=sf-dump-key>An error occurred while trying to login</span>\" => \"<span class=sf-dump-str title=\"39 characters\">An error occurred while trying to login</span>\"\n                    \"<span class=sf-dump-key>Android app URL</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Android app URL</span>\"\n                    \"<span class=sf-dump-key>App Store</span>\" => \"<span class=sf-dump-str title=\"9 characters\">App Store</span>\"\n                    \"<span class=sf-dump-key>Applied coupon &quot;:code&quot; successfully!</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Applied coupon &quot;:code&quot; successfully!</span>\"\n                    \"<span class=sf-dump-key>Apply</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Apply</span>\"\n                    \"<span class=sf-dump-key>Are you sure you have received your order? Please confirm that the package has been delivered to you?</span>\" => \"<span class=sf-dump-str title=\"101 characters\">Are you sure you have received your order? Please confirm that the package has been delivered to you?</span>\"\n                    \"<span class=sf-dump-key>Are you sure you want to delete this address?</span>\" => \"<span class=sf-dump-str title=\"45 characters\">Are you sure you want to delete this address?</span>\"\n                    \"<span class=sf-dump-key>Are you sure you want to do this?</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Are you sure you want to do this?</span>\"\n                    \"<span class=sf-dump-key>Are you sure you want to turn off the debug mode? This action cannot be undone.</span>\" => \"<span class=sf-dump-str title=\"79 characters\">Are you sure you want to turn off the debug mode? This action cannot be undone.</span>\"\n                    \"<span class=sf-dump-key>Are you sure?</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Are you sure?</span>\"\n                    \"<span class=sf-dump-key>Attract your customers with the best products.</span>\" => \"<span class=sf-dump-str title=\"46 characters\">Attract your customers with the best products.</span>\"\n                    \"<span class=sf-dump-key>Audio File</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Audio File</span>\"\n                    \"<span class=sf-dump-key>Available</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Available</span>\"\n                    \"<span class=sf-dump-key>Back</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Back</span>\"\n                    \"<span class=sf-dump-key>Back to Products</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Back to Products</span>\"\n                    \"<span class=sf-dump-key>Back to Shop</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Back to Shop</span>\"\n                    \"<span class=sf-dump-key>Back to cart</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Back to cart</span>\"\n                    \"<span class=sf-dump-key>Back to login page</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Back to login page</span>\"\n                    \"<span class=sf-dump-key>Back to shopping</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Back to shopping</span>\"\n                    \"<span class=sf-dump-key>Background color</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Background color</span>\"\n                    \"<span class=sf-dump-key>Balance</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Balance</span>\"\n                    \"<span class=sf-dump-key>Bank Code/IFSC</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Bank Code/IFSC</span>\"\n                    \"<span class=sf-dump-key>Bank Name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Bank Name</span>\"\n                    \"<span class=sf-dump-key>Bank transfer amount: &lt;strong&gt;:amount&lt;/strong&gt;</span>\" => \"<span class=sf-dump-str title=\"46 characters\">Bank transfer amount: &lt;strong&gt;:amount&lt;/strong&gt;</span>\"\n                    \"<span class=sf-dump-key>Bank transfer description: &lt;strong&gt;Payment for order :code&lt;/strong&gt;</span>\" => \"<span class=sf-dump-str title=\"67 characters\">Bank transfer description: &lt;strong&gt;Payment for order :code&lt;/strong&gt;</span>\"\n                    \"<span class=sf-dump-key>Banner</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Banner</span>\"\n                    \"<span class=sf-dump-key>Barcode &quot;:value&quot; has been duplicated on row #:row</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Barcode &quot;:value&quot; has been duplicated on row #:row</span>\"\n                    \"<span class=sf-dump-key>Become A Vendor</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Become A Vendor</span>\"\n                    \"<span class=sf-dump-key>Become Vendor</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Become Vendor</span>\"\n                    \"<span class=sf-dump-key>Before the last image</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Before the last image</span>\"\n                    \"<span class=sf-dump-key>Below :toPrice</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Below :toPrice</span>\"\n                    \"<span class=sf-dump-key>Billing information</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Billing information</span>\"\n                    \"<span class=sf-dump-key>Blog Categories</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Blog Categories</span>\"\n                    \"<span class=sf-dump-key>Blog Posts</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Blog Posts</span>\"\n                    \"<span class=sf-dump-key>Blog Search</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Blog Search</span>\"\n                    \"<span class=sf-dump-key>Blog Sidebar</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Blog Sidebar</span>\"\n                    \"<span class=sf-dump-key>Bottom</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Bottom</span>\"\n                    \"<span class=sf-dump-key>Bottom Footer sidebar</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Bottom Footer sidebar</span>\"\n                    \"<span class=sf-dump-key>Brand</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Brand</span>\"\n                    \"<span class=sf-dump-key>Brands</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Brands</span>\"\n                    \"<span class=sf-dump-key>Browse products</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Browse products</span>\"\n                    \"<span class=sf-dump-key>Business Name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Business Name</span>\"\n                    \"<span class=sf-dump-key>Button text color</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Button text color</span>\"\n                    \"<span class=sf-dump-key>Buy Now</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Buy Now</span>\"\n                    \"<span class=sf-dump-key>By</span>\" => \"<span class=sf-dump-str title=\"2 characters\">By</span>\"\n                    \"<span class=sf-dump-key>Call us 24/7</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Call us 24/7</span>\"\n                    \"<span class=sf-dump-key>Can&#039;t send message on this time, please try again later!</span>\" => \"<span class=sf-dump-str title=\"56 characters\">Can&#039;t send message on this time, please try again later!</span>\"\n                    \"<span class=sf-dump-key>Cancel</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Cancel</span>\"\n                    \"<span class=sf-dump-key>Cancel Order</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Cancel Order</span>\"\n                    \"<span class=sf-dump-key>Cancel order</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Cancel order</span>\"\n                    \"<span class=sf-dump-key>Cancel return order with reason: :reason</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Cancel return order with reason: :reason</span>\"\n                    \"<span class=sf-dump-key>Cancellation Reason</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Cancellation Reason</span>\"\n                    \"<span class=sf-dump-key>Cannot download files</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Cannot download files</span>\"\n                    \"<span class=sf-dump-key>Cannot find this customer!</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Cannot find this customer!</span>\"\n                    \"<span class=sf-dump-key>Cannot found files</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Cannot found files</span>\"\n                    \"<span class=sf-dump-key>Cannot login, no email provided!</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Cannot login, no email provided!</span>\"\n                    \"<span class=sf-dump-key>Captcha</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Captcha</span>\"\n                    \"<span class=sf-dump-key>Captcha Verification Failed!</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Captcha Verification Failed!</span>\"\n                    \"<span class=sf-dump-key>Cart</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Cart</span>\"\n                    \"<span class=sf-dump-key>Cart item ID is required!</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Cart item ID is required!</span>\"\n                    \"<span class=sf-dump-key>Cart item is not existed!</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Cart item is not existed!</span>\"\n                    \"<span class=sf-dump-key>Cart item not found</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Cart item not found</span>\"\n                    \"<span class=sf-dump-key>Cart item removed successfully</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Cart item removed successfully</span>\"\n                    \"<span class=sf-dump-key>Categories</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Categories</span>\"\n                    \"<span class=sf-dump-key>Certificate of Incorporation</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Certificate of Incorporation</span>\"\n                    \"<span class=sf-dump-key>Change Password</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Change Password</span>\"\n                    \"<span class=sf-dump-key>Change avatar</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Change avatar</span>\"\n                    \"<span class=sf-dump-key>Change copyright</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Change copyright</span>\"\n                    \"<span class=sf-dump-key>Change password</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Change password</span>\"\n                    \"<span class=sf-dump-key>Check</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Check</span>\"\n                    \"<span class=sf-dump-key>Checkout</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Checkout</span>\"\n                    \"<span class=sf-dump-key>Checkout error!</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Checkout error!</span>\"\n                    \"<span class=sf-dump-key>Checkout is only available for products from one store at a time. Please remove items from other stores before proceeding.</span>\" => \"<span class=sf-dump-str title=\"122 characters\">Checkout is only available for products from one store at a time. Please remove items from other stores before proceeding.</span>\"\n                    \"<span class=sf-dump-key>Checkout successfully!</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Checkout successfully!</span>\"\n                    \"<span class=sf-dump-key>Choose Reason</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Choose Reason</span>\"\n                    \"<span class=sf-dump-key>Choose a Reason for Order Cancellation</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Choose a Reason for Order Cancellation</span>\"\n                    \"<span class=sf-dump-key>Choose a reason...</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Choose a reason...</span>\"\n                    \"<span class=sf-dump-key>Choose categories</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Choose categories</span>\"\n                    \"<span class=sf-dump-key>Choose date format for your front theme.</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Choose date format for your front theme.</span>\"\n                    \"<span class=sf-dump-key>Choose products</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Choose products</span>\"\n                    \"<span class=sf-dump-key>City</span>\" => \"<span class=sf-dump-str title=\"4 characters\">City</span>\"\n                    \"<span class=sf-dump-key>Close</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Close</span>\"\n                    \"<span class=sf-dump-key>Color</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Color</span>\"\n                    \"<span class=sf-dump-key>Coming soon</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Coming soon</span>\"\n                    \"<span class=sf-dump-key>Comment</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Comment</span>\"\n                    \"<span class=sf-dump-key>Company address</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Company address</span>\"\n                    \"<span class=sf-dump-key>Company email</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Company email</span>\"\n                    \"<span class=sf-dump-key>Company name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Company name</span>\"\n                    \"<span class=sf-dump-key>Company tax code</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Company tax code</span>\"\n                    \"<span class=sf-dump-key>Compare</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Compare</span>\"\n                    \"<span class=sf-dump-key>Compare Product</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Compare Product</span>\"\n                    \"<span class=sf-dump-key>Completed At</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Completed At</span>\"\n                    \"<span class=sf-dump-key>Completed at</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Completed at</span>\"\n                    \"<span class=sf-dump-key>Config email templates for theme</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Config email templates for theme</span>\"\n                    \"<span class=sf-dump-key>Confirm</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Confirm</span>\"\n                    \"<span class=sf-dump-key>Confirm Delivery</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Confirm Delivery</span>\"\n                    \"<span class=sf-dump-key>Confirm password</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Confirm password</span>\"\n                    \"<span class=sf-dump-key>Confirm your password</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Confirm your password</span>\"\n                    \"<span class=sf-dump-key>Congratulations on being a vendor at :site_title</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Congratulations on being a vendor at :site_title</span>\"\n                    \"<span class=sf-dump-key>Contact box detail :number</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Contact box detail :number</span>\"\n                    \"<span class=sf-dump-key>Contact box subtitle :number</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Contact box subtitle :number</span>\"\n                    \"<span class=sf-dump-key>Contact box title :number</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Contact box title :number</span>\"\n                    \"<span class=sf-dump-key>Contact info boxes</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Contact info boxes</span>\"\n                    \"<span class=sf-dump-key>Contact us</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Contact us</span>\"\n                    \"<span class=sf-dump-key>Content</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Content</span>\"\n                    \"<span class=sf-dump-key>Continue Shopping</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Continue Shopping</span>\"\n                    \"<span class=sf-dump-key>Continue shopping</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Continue shopping</span>\"\n                    \"<span class=sf-dump-key>Copy link</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Copy link</span>\"\n                    \"<span class=sf-dump-key>Copyright</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Copyright</span>\"\n                    \"<span class=sf-dump-key>Copyright on footer of site. Using %Y to display current year.</span>\" => \"<span class=sf-dump-str title=\"62 characters\">Copyright on footer of site. Using %Y to display current year.</span>\"\n                    \"<span class=sf-dump-key>Copyright text at the bottom footer.</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Copyright text at the bottom footer.</span>\"\n                    \"<span class=sf-dump-key>Could not download updated file. Please check your license or your internet network.</span>\" => \"<span class=sf-dump-str title=\"84 characters\">Could not download updated file. Please check your license or your internet network.</span>\"\n                    \"<span class=sf-dump-key>Could not update files &amp; database.</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Could not update files &amp; database.</span>\"\n                    \"<span class=sf-dump-key>Country</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Country</span>\"\n                    \"<span class=sf-dump-key>Coupon</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Coupon</span>\"\n                    \"<span class=sf-dump-key>Coupon Discount</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Coupon Discount</span>\"\n                    \"<span class=sf-dump-key>Coupon code</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Coupon code</span>\"\n                    \"<span class=sf-dump-key>Coupon code discount amount</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Coupon code discount amount</span>\"\n                    \"<span class=sf-dump-key>Coupon code is not valid or does not apply to the products</span>\" => \"<span class=sf-dump-str title=\"58 characters\">Coupon code is not valid or does not apply to the products</span>\"\n                    \"<span class=sf-dump-key>Coupon code: &quot;:code&quot;</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Coupon code: &quot;:code&quot;</span>\"\n                    \"<span class=sf-dump-key>Coupon code: :code</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Coupon code: :code</span>\"\n                    \"<span class=sf-dump-key>Coupon codes (:count)</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Coupon codes (:count)</span>\"\n                    \"<span class=sf-dump-key>Coupons</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Coupons</span>\"\n                    \"<span class=sf-dump-key>Cover Image</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Cover Image</span>\"\n                    \"<span class=sf-dump-key>Create</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Create</span>\"\n                    \"<span class=sf-dump-key>Create Address</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Create Address</span>\"\n                    \"<span class=sf-dump-key>Create a new product &lt;a href=&quot;:url&quot;&gt;here&lt;/a&gt;</span>\" => \"<span class=sf-dump-str title=\"44 characters\">Create a new product &lt;a href=&quot;:url&quot;&gt;here&lt;/a&gt;</span>\"\n                    \"<span class=sf-dump-key>Create coupon</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Create coupon</span>\"\n                    \"<span class=sf-dump-key>Created At</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Created At</span>\"\n                    \"<span class=sf-dump-key>Created at</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Created at</span>\"\n                    \"<span class=sf-dump-key>Created shipment for order</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Created shipment for order</span>\"\n                    \"<span class=sf-dump-key>Current password</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Current password</span>\"\n                    \"<span class=sf-dump-key>Custom HTML</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Custom HTML</span>\"\n                    \"<span class=sf-dump-key>Custom Menu</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Custom Menu</span>\"\n                    \"<span class=sf-dump-key>Customer</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Customer</span>\"\n                    \"<span class=sf-dump-key>Customer ID must be a number!</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Customer ID must be a number!</span>\"\n                    \"<span class=sf-dump-key>Customer Recently Viewed Products</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Customer Recently Viewed Products</span>\"\n                    \"<span class=sf-dump-key>Customer can buy product and pay directly using Visa, Credit card via :name</span>\" => \"<span class=sf-dump-str title=\"75 characters\">Customer can buy product and pay directly using Visa, Credit card via :name</span>\"\n                    \"<span class=sf-dump-key>Customer forgot password form</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Customer forgot password form</span>\"\n                    \"<span class=sf-dump-key>Customer information</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Customer information</span>\"\n                    \"<span class=sf-dump-key>Customer login form</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Customer login form</span>\"\n                    \"<span class=sf-dump-key>Customer register form</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Customer register form</span>\"\n                    \"<span class=sf-dump-key>Customer reset password form</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Customer reset password form</span>\"\n                    \"<span class=sf-dump-key>Customer reviews</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Customer reviews</span>\"\n                    \"<span class=sf-dump-key>Customers who bought this item also bought</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Customers who bought this item also bought</span>\"\n                    \"<span class=sf-dump-key>Dashboard</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Dashboard</span>\"\n                    \"<span class=sf-dump-key>Date</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Date</span>\"\n                    \"<span class=sf-dump-key>Date Shipped</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Date Shipped</span>\"\n                    \"<span class=sf-dump-key>Date format</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Date format</span>\"\n                    \"<span class=sf-dump-key>Date of birth</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Date of birth</span>\"\n                    \"<span class=sf-dump-key>Days</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Days</span>\"\n                    \"<span class=sf-dump-key>Default</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n                    \"<span class=sf-dump-key>Default: 10 (seconds)</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Default: 10 (seconds)</span>\"\n                    \"<span class=sf-dump-key>Default: 3</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Default: 3</span>\"\n                    \"<span class=sf-dump-key>Delete</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Delete</span>\"\n                    \"<span class=sf-dump-key>Delete account</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Delete account</span>\"\n                    \"<span class=sf-dump-key>Delete ads.txt file</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Delete ads.txt file</span>\"\n                    \"<span class=sf-dump-key>Delete your account</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Delete your account</span>\"\n                    \"<span class=sf-dump-key>Deleted review successfully!</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Deleted review successfully!</span>\"\n                    \"<span class=sf-dump-key>Delivered</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Delivered</span>\"\n                    \"<span class=sf-dump-key>Delivery Notes</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Delivery Notes</span>\"\n                    \"<span class=sf-dump-key>Delivery Notes:</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Delivery Notes:</span>\"\n                    \"<span class=sf-dump-key>Description</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Description</span>\"\n                    \"<span class=sf-dump-key>Device ID is required!</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Device ID is required!</span>\"\n                    \"<span class=sf-dump-key>Discount</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Discount</span>\"\n                    \"<span class=sf-dump-key>Discount :amount</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Discount :amount</span>\"\n                    \"<span class=sf-dump-key>Discount :percentage%</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Discount :percentage%</span>\"\n                    \"<span class=sf-dump-key>Discount amount</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Discount amount</span>\"\n                    \"<span class=sf-dump-key>Discount promotion</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Discount promotion</span>\"\n                    \"<span class=sf-dump-key>Display blog posts</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Display blog posts</span>\"\n                    \"<span class=sf-dump-key>Display on pages</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Display on pages</span>\"\n                    \"<span class=sf-dump-key>Display posts count?</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Display posts count?</span>\"\n                    \"<span class=sf-dump-key>Display recent blog posts</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Display recent blog posts</span>\"\n                    \"<span class=sf-dump-key>Do you really want to delete the review?</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Do you really want to delete the review?</span>\"\n                    \"<span class=sf-dump-key>Do you want to cancel?</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Do you want to cancel?</span>\"\n                    \"<span class=sf-dump-key>Do you want to delete this image?</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Do you want to delete this image?</span>\"\n                    \"<span class=sf-dump-key>Documents</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Documents</span>\"\n                    \"<span class=sf-dump-key>Don&#039;t Miss Out! This promotion will expires in</span>\" => \"<span class=sf-dump-str title=\"46 characters\">Don&#039;t Miss Out! This promotion will expires in</span>\"\n                    \"<span class=sf-dump-key>Don&#039;t have an account?</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Don&#039;t have an account?</span>\"\n                    \"<span class=sf-dump-key>Don&#039;t show this popup again</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Don&#039;t show this popup again</span>\"\n                    \"<span class=sf-dump-key>Download Apps</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Download Apps</span>\"\n                    \"<span class=sf-dump-key>Download all files</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Download all files</span>\"\n                    \"<span class=sf-dump-key>Download apps</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Download apps</span>\"\n                    \"<span class=sf-dump-key>Download invoice</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Download invoice</span>\"\n                    \"<span class=sf-dump-key>Download product &quot;:name&quot; with external links</span>\" => \"<span class=sf-dump-str title=\"44 characters\">Download product &quot;:name&quot; with external links</span>\"\n                    \"<span class=sf-dump-key>Downloads</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Downloads</span>\"\n                    \"<span class=sf-dump-key>Drop Certificate of Incorporation here or click to upload</span>\" => \"<span class=sf-dump-str title=\"57 characters\">Drop Certificate of Incorporation here or click to upload</span>\"\n                    \"<span class=sf-dump-key>Drop Government ID here or click to upload</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Drop Government ID here or click to upload</span>\"\n                    \"<span class=sf-dump-key>Drop files here or click to upload.</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Drop files here or click to upload.</span>\"\n                    \"<span class=sf-dump-key>Earnings</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Earnings</span>\"\n                    \"<span class=sf-dump-key>Earnings in :label</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Earnings in :label</span>\"\n                    \"<span class=sf-dump-key>Edit</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Edit</span>\"\n                    \"<span class=sf-dump-key>Edit Address #:id</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Edit Address #:id</span>\"\n                    \"<span class=sf-dump-key>Edit this shortcode</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Edit this shortcode</span>\"\n                    \"<span class=sf-dump-key>Edit this widget</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Edit this widget</span>\"\n                    \"<span class=sf-dump-key>Email</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Email</span>\"\n                    \"<span class=sf-dump-key>Email (optional)</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Email (optional)</span>\"\n                    \"<span class=sf-dump-key>Email :store</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Email :store</span>\"\n                    \"<span class=sf-dump-key>Email Address</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Email Address</span>\"\n                    \"<span class=sf-dump-key>Email address</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Email address</span>\"\n                    \"<span class=sf-dump-key>Email or Phone number</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Email or Phone number</span>\"\n                    \"<span class=sf-dump-key>Email or phone</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Email or phone</span>\"\n                    \"<span class=sf-dump-key>Empty cart successfully!</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Empty cart successfully!</span>\"\n                    \"<span class=sf-dump-key>Enable Facebook chat?</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Enable Facebook chat?</span>\"\n                    \"<span class=sf-dump-key>Enable Facebook comment in post detail page?</span>\" => \"<span class=sf-dump-str title=\"44 characters\">Enable Facebook comment in post detail page?</span>\"\n                    \"<span class=sf-dump-key>Enable Facebook comment in the product page?</span>\" => \"<span class=sf-dump-str title=\"44 characters\">Enable Facebook comment in the product page?</span>\"\n                    \"<span class=sf-dump-key>Enable Newsletter Popup</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Enable Newsletter Popup</span>\"\n                    \"<span class=sf-dump-key>Enable Preloader?</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Enable Preloader?</span>\"\n                    \"<span class=sf-dump-key>Enable dark mode</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Enable dark mode</span>\"\n                    \"<span class=sf-dump-key>Enable lazy loading</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Enable lazy loading</span>\"\n                    \"<span class=sf-dump-key>Enable light mode</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Enable light mode</span>\"\n                    \"<span class=sf-dump-key>Enable newsletter popup?</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Enable newsletter popup?</span>\"\n                    \"<span class=sf-dump-key>Enable sticky header on mobile?</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Enable sticky header on mobile?</span>\"\n                    \"<span class=sf-dump-key>Enable sticky header?</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Enable sticky header?</span>\"\n                    \"<span class=sf-dump-key>End date</span>\" => \"<span class=sf-dump-str title=\"8 characters\">End date</span>\"\n                    \"<span class=sf-dump-key>End in</span>\" => \"<span class=sf-dump-str title=\"6 characters\">End in</span>\"\n                    \"<span class=sf-dump-key>Enter API key into the box in right hand</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Enter API key into the box in right hand</span>\"\n                    \"<span class=sf-dump-key>Enter Client ID, Secret into the box in right hand</span>\" => \"<span class=sf-dump-str title=\"50 characters\">Enter Client ID, Secret into the box in right hand</span>\"\n                    \"<span class=sf-dump-key>Enter Coupon Code</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Enter Coupon Code</span>\"\n                    \"<span class=sf-dump-key>Enter Public, Secret into the box in right hand</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Enter Public, Secret into the box in right hand</span>\"\n                    \"<span class=sf-dump-key>Enter Store ID and Store Password (API/Secret key) into the box in right hand</span>\" => \"<span class=sf-dump-str title=\"77 characters\">Enter Store ID and Store Password (API/Secret key) into the box in right hand</span>\"\n                    \"<span class=sf-dump-key>Enter Your Email</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Enter Your Email</span>\"\n                    \"<span class=sf-dump-key>Enter coupon code</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Enter coupon code</span>\"\n                    \"<span class=sf-dump-key>Enter coupon code...</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Enter coupon code...</span>\"\n                    \"<span class=sf-dump-key>Enter the order ID</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Enter the order ID</span>\"\n                    \"<span class=sf-dump-key>Enter your email</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Enter your email</span>\"\n                    \"<span class=sf-dump-key>Enter your phone number</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Enter your phone number</span>\"\n                    \"<span class=sf-dump-key>Error</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Error</span>\"\n                    \"<span class=sf-dump-key>Error when processing payment via :paymentType!</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Error when processing payment via :paymentType!</span>\"\n                    \"<span class=sf-dump-key>Estimate Date Shipped</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Estimate Date Shipped</span>\"\n                    \"<span class=sf-dump-key>Ex: 0943243332</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Ex: 0943243332</span>\"\n                    \"<span class=sf-dump-key>Ex: My Shop</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Ex: My Shop</span>\"\n                    \"<span class=sf-dump-key>Exception</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Exception</span>\"\n                    \"<span class=sf-dump-key>Explore and add items to get started</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Explore and add items to get started</span>\"\n                    \"<span class=sf-dump-key>External link downloads</span>\" => \"<span class=sf-dump-str title=\"23 characters\">External link downloads</span>\"\n                    \"<span class=sf-dump-key>FAQs</span>\" => \"<span class=sf-dump-str title=\"4 characters\">FAQs</span>\"\n                    \"<span class=sf-dump-key>Facebook</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Facebook</span>\"\n                    \"<span class=sf-dump-key>Facebook Admin ID</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Facebook Admin ID</span>\"\n                    \"<span class=sf-dump-key>Facebook Admins</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Facebook Admins</span>\"\n                    \"<span class=sf-dump-key>Facebook App ID</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Facebook App ID</span>\"\n                    \"<span class=sf-dump-key>Facebook Integration</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Facebook Integration</span>\"\n                    \"<span class=sf-dump-key>Facebook admins to manage comments :link</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Facebook admins to manage comments :link</span>\"\n                    \"<span class=sf-dump-key>Facebook page ID</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Facebook page ID</span>\"\n                    \"<span class=sf-dump-key>Featured</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Featured</span>\"\n                    \"<span class=sf-dump-key>Featured Brands</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Featured Brands</span>\"\n                    \"<span class=sf-dump-key>Featured Product Categories</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Featured Product Categories</span>\"\n                    \"<span class=sf-dump-key>Featured products</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Featured products</span>\"\n                    \"<span class=sf-dump-key>Fee</span>\" => \"<span class=sf-dump-str title=\"3 characters\">Fee</span>\"\n                    \"<span class=sf-dump-key>Fees</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Fees</span>\"\n                    \"<span class=sf-dump-key>Filter</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Filter</span>\"\n                    \"<span class=sf-dump-key>Filter Products</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Filter Products</span>\"\n                    \"<span class=sf-dump-key>Find</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Find</span>\"\n                    \"<span class=sf-dump-key>Fix it for me</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Fix it for me</span>\"\n                    \"<span class=sf-dump-key>Flash sale</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Flash sale</span>\"\n                    \"<span class=sf-dump-key>Footer sidebar</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Footer sidebar</span>\"\n                    \"<span class=sf-dump-key>For devices with width from 768px to 1200px, if empty, will use the image from the desktop.</span>\" => \"<span class=sf-dump-str title=\"91 characters\">For devices with width from 768px to 1200px, if empty, will use the image from the desktop.</span>\"\n                    \"<span class=sf-dump-key>For devices with width less than 768px, if empty, will use the image from the tablet.</span>\" => \"<span class=sf-dump-str title=\"85 characters\">For devices with width less than 768px, if empty, will use the image from the tablet.</span>\"\n                    \"<span class=sf-dump-key>Forgot Password</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Forgot Password</span>\"\n                    \"<span class=sf-dump-key>Forgot password?</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Forgot password?</span>\"\n                    \"<span class=sf-dump-key>Free</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Free</span>\"\n                    \"<span class=sf-dump-key>Free shipping</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Free shipping</span>\"\n                    \"<span class=sf-dump-key>Free shipping for all orders</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Free shipping for all orders</span>\"\n                    \"<span class=sf-dump-key>Free shipping to &lt;strong&gt;:target&lt;/strong&gt;</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Free shipping to &lt;strong&gt;:target&lt;/strong&gt;</span>\"\n                    \"<span class=sf-dump-key>From :fromPrice to :toPrice</span>\" => \"<span class=sf-dump-str title=\"27 characters\">From :fromPrice to :toPrice</span>\"\n                    \"<span class=sf-dump-key>From your account dashboard you can view your &lt;a class=&quot;text-primary&quot; href=&quot;:order&quot;&gt;recent orders&lt;/a&gt;, manage your &lt;a class=&quot;text-primary&quot; href=&quot;:addresses&quot;&gt;shipping and billing addresses&lt;/a&gt;, and &lt;a class=&quot;text-primary&quot; href=&quot;:edit_account&quot;&gt;edit your password and account details&lt;/a&gt;.</span>\" => \"<span class=sf-dump-str title=\"285 characters\">From your account dashboard you can view your &lt;a class=&quot;text-primary&quot; href=&quot;:order&quot;&gt;recent orders&lt;/a&gt;, manage your &lt;a class=&quot;text-primary&quot; href=&quot;:addresses&quot;&gt;shipping and billing addresses&lt;/a&gt;, and &lt;a class=&quot;text-primary&quot; href=&quot;:edit_account&quot;&gt;edit your password and account details&lt;/a&gt;.</span>\"\n                    \"<span class=sf-dump-key>Full Name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Full Name</span>\"\n                    \"<span class=sf-dump-key>Full name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Full name</span>\"\n                    \"<span class=sf-dump-key>Full width</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Full width</span>\"\n                    \"<span class=sf-dump-key>Functions</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Functions</span>\"\n                    \"<span class=sf-dump-key>Funding Source</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Funding Source</span>\"\n                    \"<span class=sf-dump-key>Get 25% Discount</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Get 25% Discount</span>\"\n                    \"<span class=sf-dump-key>Get In Touch</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Get In Touch</span>\"\n                    \"<span class=sf-dump-key>Gift</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Gift</span>\"\n                    \"<span class=sf-dump-key>Go To Cart</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Go To Cart</span>\"\n                    \"<span class=sf-dump-key>Go back home</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Go back home</span>\"\n                    \"<span class=sf-dump-key>Go to :link to change the copyright text.</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Go to :link to change the copyright text.</span>\"\n                    \"<span class=sf-dump-key>Go to homepage</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Go to homepage</span>\"\n                    \"<span class=sf-dump-key>Google Maps</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Google Maps</span>\"\n                    \"<span class=sf-dump-key>Google Play</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Google Play</span>\"\n                    \"<span class=sf-dump-key>Government ID</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Government ID</span>\"\n                    \"<span class=sf-dump-key>HTML code</span>\" => \"<span class=sf-dump-str title=\"9 characters\">HTML code</span>\"\n                    \"<span class=sf-dump-key>Header button background color</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Header button background color</span>\"\n                    \"<span class=sf-dump-key>Header button text color</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Header button text color</span>\"\n                    \"<span class=sf-dump-key>Header text accent color</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Header text accent color</span>\"\n                    \"<span class=sf-dump-key>Header text color</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Header text color</span>\"\n                    \"<span class=sf-dump-key>Header text hover color</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Header text hover color</span>\"\n                    \"<span class=sf-dump-key>Height</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Height</span>\"\n                    \"<span class=sf-dump-key>Hello</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Hello</span>\"\n                    \"<span class=sf-dump-key>Hello &lt;strong&gt;:name&lt;/strong&gt;,</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Hello &lt;strong&gt;:name&lt;/strong&gt;,</span>\"\n                    \"<span class=sf-dump-key>Home</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Home</span>\"\n                    \"<span class=sf-dump-key>Homepage</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Homepage</span>\"\n                    \"<span class=sf-dump-key>Horizontal</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Horizontal</span>\"\n                    \"<span class=sf-dump-key>Hotline</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Hotline</span>\"\n                    \"<span class=sf-dump-key>Hours</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Hours</span>\"\n                    \"<span class=sf-dump-key>Humanitarian Donation</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Humanitarian Donation</span>\"\n                    \"<span class=sf-dump-key>I agree to the :link</span>\" => \"<span class=sf-dump-str title=\"20 characters\">I agree to the :link</span>\"\n                    \"<span class=sf-dump-key>I agree to the Terms and Privacy Policy</span>\" => \"<span class=sf-dump-str title=\"39 characters\">I agree to the Terms and Privacy Policy</span>\"\n                    \"<span class=sf-dump-key>I am a customer</span>\" => \"<span class=sf-dump-str title=\"15 characters\">I am a customer</span>\"\n                    \"<span class=sf-dump-key>I am a vendor</span>\" => \"<span class=sf-dump-str title=\"13 characters\">I am a vendor</span>\"\n                    \"<span class=sf-dump-key>I&#039;m shopping for...</span>\" => \"<span class=sf-dump-str title=\"19 characters\">I&#039;m shopping for...</span>\"\n                    \"<span class=sf-dump-key>ID</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ID</span>\"\n                    \"<span class=sf-dump-key>ID number</span>\" => \"<span class=sf-dump-str title=\"9 characters\">ID number</span>\"\n                    \"<span class=sf-dump-key>Icon</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Icon</span>\"\n                    \"<span class=sf-dump-key>Icon :number</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Icon :number</span>\"\n                    \"<span class=sf-dump-key>Icon Image (It will override icon above if set)</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Icon Image (It will override icon above if set)</span>\"\n                    \"<span class=sf-dump-key>Icon image</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Icon image</span>\"\n                    \"<span class=sf-dump-key>Icon image (It will override icon above if set)</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Icon image (It will override icon above if set)</span>\"\n                    \"<span class=sf-dump-key>If you are the administrator and you can&#039;t access your site after enabling maintenance mode, just need to delete file &lt;strong&gt;storage/framework/down&lt;/strong&gt; to turn-off maintenance mode.</span>\" => \"<span class=sf-dump-str title=\"187 characters\">If you are the administrator and you can&#039;t access your site after enabling maintenance mode, just need to delete file &lt;strong&gt;storage/framework/down&lt;/strong&gt; to turn-off maintenance mode.</span>\"\n                    \"<span class=sf-dump-key>If you need help, contact us at :mail.</span>\" => \"<span class=sf-dump-str title=\"38 characters\">If you need help, contact us at :mail.</span>\"\n                    \"<span class=sf-dump-key>Image</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Image</span>\"\n                    \"<span class=sf-dump-key>Image for newsletter popup</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Image for newsletter popup</span>\"\n                    \"<span class=sf-dump-key>Images from customer (:count)</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Images from customer (:count)</span>\"\n                    \"<span class=sf-dump-key>In Transit</span>\" => \"<span class=sf-dump-str title=\"10 characters\">In Transit</span>\"\n                    \"<span class=sf-dump-key>In stock</span>\" => \"<span class=sf-dump-str title=\"8 characters\">In stock</span>\"\n                    \"<span class=sf-dump-key>Includes Completed, Pending, and Processing statuses</span>\" => \"<span class=sf-dump-str title=\"52 characters\">Includes Completed, Pending, and Processing statuses</span>\"\n                    \"<span class=sf-dump-key>Install plugin from Marketplace</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Install plugin from Marketplace</span>\"\n                    \"<span class=sf-dump-key>Insufficient balance or no bank information</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Insufficient balance or no bank information</span>\"\n                    \"<span class=sf-dump-key>Internal Server Error</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Internal Server Error</span>\"\n                    \"<span class=sf-dump-key>Invalid Data!</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Invalid Data!</span>\"\n                    \"<span class=sf-dump-key>Invalid Transaction!</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Invalid Transaction!</span>\"\n                    \"<span class=sf-dump-key>Invalid data send</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Invalid data send</span>\"\n                    \"<span class=sf-dump-key>Invalid signature of vendor info</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Invalid signature of vendor info</span>\"\n                    \"<span class=sf-dump-key>Invalid step.</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Invalid step.</span>\"\n                    \"<span class=sf-dump-key>InvalidStateException occurred while trying to login</span>\" => \"<span class=sf-dump-str title=\"52 characters\">InvalidStateException occurred while trying to login</span>\"\n                    \"<span class=sf-dump-key>Invoice detail :code</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Invoice detail :code</span>\"\n                    \"<span class=sf-dump-key>Invoices</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Invoices</span>\"\n                    \"<span class=sf-dump-key>It is optional. If you have UPI ID, you can provide it here. Learn more: https://support.google.com/pay/india/answer/10331134?hl=en</span>\" => \"<span class=sf-dump-str title=\"131 characters\">It is optional. If you have UPI ID, you can provide it here. Learn more: https://support.google.com/pay/india/answer/10331134?hl=en</span>\"\n                    \"<span class=sf-dump-key>It looks as through there are no activities here.</span>\" => \"<span class=sf-dump-str title=\"49 characters\">It looks as through there are no activities here.</span>\"\n                    \"<span class=sf-dump-key>It seems we can&#039;t find what you&#039;re looking for. Perhaps searching can help or go back to</span>\" => \"<span class=sf-dump-str title=\"88 characters\">It seems we can&#039;t find what you&#039;re looking for. Perhaps searching can help or go back to</span>\"\n                    \"<span class=sf-dump-key>It will replace Icon Font if it is present.</span>\" => \"<span class=sf-dump-str title=\"43 characters\">It will replace Icon Font if it is present.</span>\"\n                    \"<span class=sf-dump-key>Items Count</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Items Count</span>\"\n                    \"<span class=sf-dump-key>Items Earning Sales: :amount</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Items Earning Sales: :amount</span>\"\n                    \"<span class=sf-dump-key>Joined on :date</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Joined on :date</span>\"\n                    \"<span class=sf-dump-key>Key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">Key</span>\"\n                    \"<span class=sf-dump-key>Language</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Language</span>\"\n                    \"<span class=sf-dump-key>Last update</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Last update</span>\"\n                    \"<span class=sf-dump-key>Latest</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Latest</span>\"\n                    \"<span class=sf-dump-key>Lazy load images</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Lazy load images</span>\"\n                    \"<span class=sf-dump-key>Lazy load placeholder image (250x250px)</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Lazy load placeholder image (250x250px)</span>\"\n                    \"<span class=sf-dump-key>Learn more</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Learn more</span>\"\n                    \"<span class=sf-dump-key>Learn more about Twig template: :url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Learn more about Twig template: :url</span>\"\n                    \"<span class=sf-dump-key>Leave categories empty if you want to show posts from all categories.</span>\" => \"<span class=sf-dump-str title=\"69 characters\">Leave categories empty if you want to show posts from all categories.</span>\"\n                    \"<span class=sf-dump-key>Leave it empty to use the default description from Theme options -&gt; General.</span>\" => \"<span class=sf-dump-str title=\"76 characters\">Leave it empty to use the default description from Theme options -&gt; General.</span>\"\n                    \"<span class=sf-dump-key>Left :left</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Left :left</span>\"\n                    \"<span class=sf-dump-key>License</span>\" => \"<span class=sf-dump-str title=\"7 characters\">License</span>\"\n                    \"<span class=sf-dump-key>License Activation</span>\" => \"<span class=sf-dump-str title=\"18 characters\">License Activation</span>\"\n                    \"<span class=sf-dump-key>Limit</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Limit</span>\"\n                    \"<span class=sf-dump-key>Limit number of categories</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Limit number of categories</span>\"\n                    \"<span class=sf-dump-key>Limit number of products</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Limit number of products</span>\"\n                    \"<span class=sf-dump-key>LinkedIn</span>\" => \"<span class=sf-dump-str title=\"8 characters\">LinkedIn</span>\"\n                    \"<span class=sf-dump-key>List of product categories</span>\" => \"<span class=sf-dump-str title=\"26 characters\">List of product categories</span>\"\n                    \"<span class=sf-dump-key>Log files</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Log files</span>\"\n                    \"<span class=sf-dump-key>Login</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Login</span>\"\n                    \"<span class=sf-dump-key>Login to your account</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Login to your account</span>\"\n                    \"<span class=sf-dump-key>Login with social networks</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Login with social networks</span>\"\n                    \"<span class=sf-dump-key>Logo</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Logo</span>\"\n                    \"<span class=sf-dump-key>Logo height (px)</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Logo height (px)</span>\"\n                    \"<span class=sf-dump-key>Logout</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Logout</span>\"\n                    \"<span class=sf-dump-key>Looks like there are no reviews yet.</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Looks like there are no reviews yet.</span>\"\n                    \"<span class=sf-dump-key>Lost your password? Please enter your username or email address. You will receive a link to create a new password via email.</span>\" => \"<span class=sf-dump-str title=\"124 characters\">Lost your password? Please enter your username or email address. You will receive a link to create a new password via email.</span>\"\n                    \"<span class=sf-dump-key>Manage Invoices</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Manage Invoices</span>\"\n                    \"<span class=sf-dump-key>Marketplace Stores</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Marketplace Stores</span>\"\n                    \"<span class=sf-dump-key>Math Captcha</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Math Captcha</span>\"\n                    \"<span class=sf-dump-key>Math Captcha Verification Failed!</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Math Captcha Verification Failed!</span>\"\n                    \"<span class=sf-dump-key>Maximum order quantity is :qty, please check your cart and retry again!</span>\" => \"<span class=sf-dump-str title=\"71 characters\">Maximum order quantity is :qty, please check your cart and retry again!</span>\"\n                    \"<span class=sf-dump-key>Maximum order quantity of product :product is :quantity!</span>\" => \"<span class=sf-dump-str title=\"57 characters\">Maximum order quantity of product :product is :quantity! </span>\"\n                    \"<span class=sf-dump-key>Maximum quantity is :max!</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Maximum quantity is :max!</span>\"\n                    \"<span class=sf-dump-key>Media - Audio</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Media - Audio</span>\"\n                    \"<span class=sf-dump-key>Media - Video</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Media - Video</span>\"\n                    \"<span class=sf-dump-key>Media URL</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Media URL</span>\"\n                    \"<span class=sf-dump-key>Menu</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Menu</span>\"\n                    \"<span class=sf-dump-key>Merchandise</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Merchandise</span>\"\n                    \"<span class=sf-dump-key>Messages</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Messages</span>\"\n                    \"<span class=sf-dump-key>Minimum order amount is :amount, you need to buy more :more to place an order!</span>\" => \"<span class=sf-dump-str title=\"78 characters\">Minimum order amount is :amount, you need to buy more :more to place an order!</span>\"\n                    \"<span class=sf-dump-key>Minimum order amount to use :name payment method is :amount, you need to buy more :more to place an order!</span>\" => \"<span class=sf-dump-str title=\"106 characters\">Minimum order amount to use :name payment method is :amount, you need to buy more :more to place an order!</span>\"\n                    \"<span class=sf-dump-key>Minimum order amount to use Bank Transfer payment method is :amount, you need to buy more :more to place an order!</span>\" => \"<span class=sf-dump-str title=\"114 characters\">Minimum order amount to use Bank Transfer payment method is :amount, you need to buy more :more to place an order!</span>\"\n                    \"<span class=sf-dump-key>Minimum order amount to use COD (Cash On Delivery) payment method is :amount, you need to buy more :more to place an order!</span>\" => \"<span class=sf-dump-str title=\"123 characters\">Minimum order amount to use COD (Cash On Delivery) payment method is :amount, you need to buy more :more to place an order!</span>\"\n                    \"<span class=sf-dump-key>Minimum order quantity is :qty, you need to buy more :more to place an order!</span>\" => \"<span class=sf-dump-str title=\"77 characters\">Minimum order quantity is :qty, you need to buy more :more to place an order!</span>\"\n                    \"<span class=sf-dump-key>Minimum order quantity of product :product is :quantity, you need to buy more :more to place an order!</span>\" => \"<span class=sf-dump-str title=\"103 characters\">Minimum order quantity of product :product is :quantity, you need to buy more :more to place an order! </span>\"\n                    \"<span class=sf-dump-key>Minus</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Minus</span>\"\n                    \"<span class=sf-dump-key>Minutes</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Minutes</span>\"\n                    \"<span class=sf-dump-key>Missing documentations! Please upload your certificate of incorporation and government ID to continue.</span>\" => \"<span class=sf-dump-str title=\"102 characters\">Missing documentations! Please upload your certificate of incorporation and government ID to continue.</span>\"\n                    \"<span class=sf-dump-key>Mobile Image</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Mobile Image</span>\"\n                    \"<span class=sf-dump-key>Moderator&#039;s note</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Moderator&#039;s note</span>\"\n                    \"<span class=sf-dump-key>More Products from :store</span>\" => \"<span class=sf-dump-str title=\"25 characters\">More Products from :store</span>\"\n                    \"<span class=sf-dump-key>My Profile</span>\" => \"<span class=sf-dump-str title=\"10 characters\">My Profile</span>\"\n                    \"<span class=sf-dump-key>NEW STORE WE BE LAUNCHED IN</span>\" => \"<span class=sf-dump-str title=\"27 characters\">NEW STORE WE BE LAUNCHED IN</span>\"\n                    \"<span class=sf-dump-key>Name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n                    \"<span class=sf-dump-key>Name: A-Z</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Name: A-Z</span>\"\n                    \"<span class=sf-dump-key>Name: Z-A</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Name: Z-A</span>\"\n                    \"<span class=sf-dump-key>New password</span>\" => \"<span class=sf-dump-str title=\"12 characters\">New password</span>\"\n                    \"<span class=sf-dump-key>Newest</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Newest</span>\"\n                    \"<span class=sf-dump-key>Newsletter Form</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Newsletter Form</span>\"\n                    \"<span class=sf-dump-key>Newsletter Popup</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Newsletter Popup</span>\"\n                    \"<span class=sf-dump-key>Newsletter popup delay time (seconds)</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Newsletter popup delay time (seconds)</span>\"\n                    \"<span class=sf-dump-key>Newsletter popup description</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Newsletter popup description</span>\"\n                    \"<span class=sf-dump-key>Newsletter popup description (Default: Subscribe to the mailing list to receive updates on new arrivals, special offers and our promotions.)</span>\" => \"<span class=sf-dump-str title=\"140 characters\">Newsletter popup description (Default: Subscribe to the mailing list to receive updates on new arrivals, special offers and our promotions.)</span>\"\n                    \"<span class=sf-dump-key>Newsletter popup title</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Newsletter popup title</span>\"\n                    \"<span class=sf-dump-key>Newsletter popup title (Default: Get 25% Discount)</span>\" => \"<span class=sf-dump-str title=\"50 characters\">Newsletter popup title (Default: Get 25% Discount)</span>\"\n                    \"<span class=sf-dump-key>No</span>\" => \"<span class=sf-dump-str title=\"2 characters\">No</span>\"\n                    \"<span class=sf-dump-key>No addresses!</span>\" => \"<span class=sf-dump-str title=\"13 characters\">No addresses!</span>\"\n                    \"<span class=sf-dump-key>No digital products!</span>\" => \"<span class=sf-dump-str title=\"20 characters\">No digital products!</span>\"\n                    \"<span class=sf-dump-key>No order return requests yet!</span>\" => \"<span class=sf-dump-str title=\"29 characters\">No order return requests yet!</span>\"\n                    \"<span class=sf-dump-key>No orders has been made yet.</span>\" => \"<span class=sf-dump-str title=\"28 characters\">No orders has been made yet.</span>\"\n                    \"<span class=sf-dump-key>No orders yet!</span>\" => \"<span class=sf-dump-str title=\"14 characters\">No orders yet!</span>\"\n                    \"<span class=sf-dump-key>No orders!</span>\" => \"<span class=sf-dump-str title=\"10 characters\">No orders!</span>\"\n                    \"<span class=sf-dump-key>No payment charge. Please try again!</span>\" => \"<span class=sf-dump-str title=\"36 characters\">No payment charge. Please try again!</span>\"\n                    \"<span class=sf-dump-key>No product in wishlist!</span>\" => \"<span class=sf-dump-str title=\"23 characters\">No product in wishlist!</span>\"\n                    \"<span class=sf-dump-key>No products found.</span>\" => \"<span class=sf-dump-str title=\"18 characters\">No products found.</span>\"\n                    \"<span class=sf-dump-key>No products in cart</span>\" => \"<span class=sf-dump-str title=\"19 characters\">No products in cart</span>\"\n                    \"<span class=sf-dump-key>No products in cart. :link!</span>\" => \"<span class=sf-dump-str title=\"27 characters\">No products in cart. :link!</span>\"\n                    \"<span class=sf-dump-key>No products in compare list!</span>\" => \"<span class=sf-dump-str title=\"28 characters\">No products in compare list!</span>\"\n                    \"<span class=sf-dump-key>No products in the cart.</span>\" => \"<span class=sf-dump-str title=\"24 characters\">No products in the cart.</span>\"\n                    \"<span class=sf-dump-key>No products!</span>\" => \"<span class=sf-dump-str title=\"12 characters\">No products!</span>\"\n                    \"<span class=sf-dump-key>No results found</span>\" => \"<span class=sf-dump-str title=\"16 characters\">No results found</span>\"\n                    \"<span class=sf-dump-key>No results found!</span>\" => \"<span class=sf-dump-str title=\"17 characters\">No results found!</span>\"\n                    \"<span class=sf-dump-key>No reviews yet!</span>\" => \"<span class=sf-dump-str title=\"15 characters\">No reviews yet!</span>\"\n                    \"<span class=sf-dump-key>No reviews!</span>\" => \"<span class=sf-dump-str title=\"11 characters\">No reviews!</span>\"\n                    \"<span class=sf-dump-key>No shipping methods available!</span>\" => \"<span class=sf-dump-str title=\"30 characters\">No shipping methods available!</span>\"\n                    \"<span class=sf-dump-key>No shipping methods were found with your provided shipping information!</span>\" => \"<span class=sf-dump-str title=\"71 characters\">No shipping methods were found with your provided shipping information!</span>\"\n                    \"<span class=sf-dump-key>Not Available</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Not Available</span>\"\n                    \"<span class=sf-dump-key>Not available</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Not available</span>\"\n                    \"<span class=sf-dump-key>Not available in COD payment option.</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Not available in COD payment option.</span>\"\n                    \"<span class=sf-dump-key>Note</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Note</span>\"\n                    \"<span class=sf-dump-key>Notes about your order, e.g. special notes for delivery.</span>\" => \"<span class=sf-dump-str title=\"56 characters\">Notes about your order, e.g. special notes for delivery.</span>\"\n                    \"<span class=sf-dump-key>Number categories to display</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Number categories to display</span>\"\n                    \"<span class=sf-dump-key>Number posts to display</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Number posts to display</span>\"\n                    \"<span class=sf-dump-key>Number tags to display</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Number tags to display</span>\"\n                    \"<span class=sf-dump-key>Ohh! Page not found</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Ohh! Page not found</span>\"\n                    \"<span class=sf-dump-key>Oldest</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Oldest</span>\"\n                    \"<span class=sf-dump-key>One or all products are not enough quantity so cannot update!</span>\" => \"<span class=sf-dump-str title=\"61 characters\">One or all products are not enough quantity so cannot update!</span>\"\n                    \"<span class=sf-dump-key>Oops! Something Went Wrong.</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Oops! Something Went Wrong.</span>\"\n                    \"<span class=sf-dump-key>Open user menu</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Open user menu</span>\"\n                    \"<span class=sf-dump-key>Or you can upload a new one, the old one will be replaced.</span>\" => \"<span class=sf-dump-str title=\"58 characters\">Or you can upload a new one, the old one will be replaced.</span>\"\n                    \"<span class=sf-dump-key>Order ID</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Order ID</span>\"\n                    \"<span class=sf-dump-key>Order ID number</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Order ID number</span>\"\n                    \"<span class=sf-dump-key>Order Return Request not found!</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Order Return Request not found!</span>\"\n                    \"<span class=sf-dump-key>Order Return Requests</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Order Return Requests</span>\"\n                    \"<span class=sf-dump-key>Order Return Requests :id</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Order Return Requests :id</span>\"\n                    \"<span class=sf-dump-key>Order Returns</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Order Returns</span>\"\n                    \"<span class=sf-dump-key>Order Tracking</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Order Tracking</span>\"\n                    \"<span class=sf-dump-key>Order completed at</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Order completed at</span>\"\n                    \"<span class=sf-dump-key>Order detail</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Order detail</span>\"\n                    \"<span class=sf-dump-key>Order detail :id</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Order detail :id</span>\"\n                    \"<span class=sf-dump-key>Order information</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Order information</span>\"\n                    \"<span class=sf-dump-key>Order is created from checkout page</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Order is created from checkout page</span>\"\n                    \"<span class=sf-dump-key>Order is created from the checkout page</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Order is created from the checkout page</span>\"\n                    \"<span class=sf-dump-key>Order not found!</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Order not found!</span>\"\n                    \"<span class=sf-dump-key>Order notes</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Order notes</span>\"\n                    \"<span class=sf-dump-key>Order number</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Order number</span>\"\n                    \"<span class=sf-dump-key>Order status</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Order status</span>\"\n                    \"<span class=sf-dump-key>Order successfully at :site_title</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Order successfully at :site_title</span>\"\n                    \"<span class=sf-dump-key>Order successfully. Order number :id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Order successfully. Order number :id</span>\"\n                    \"<span class=sf-dump-key>Order tracking</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Order tracking</span>\"\n                    \"<span class=sf-dump-key>Order tracking :code</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Order tracking :code</span>\"\n                    \"<span class=sf-dump-key>Order was cancelled by customer :customer</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Order was cancelled by customer :customer</span>\"\n                    \"<span class=sf-dump-key>Order was cancelled by customer :customer with reason :reason</span>\" => \"<span class=sf-dump-str title=\"61 characters\">Order was cancelled by customer :customer with reason :reason</span>\"\n                    \"<span class=sf-dump-key>Order was confirmed delivery by customer :customer</span>\" => \"<span class=sf-dump-str title=\"50 characters\">Order was confirmed delivery by customer :customer</span>\"\n                    \"<span class=sf-dump-key>Order was created from checkout page</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Order was created from checkout page</span>\"\n                    \"<span class=sf-dump-key>Ordered at</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Ordered at</span>\"\n                    \"<span class=sf-dump-key>Orders</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Orders</span>\"\n                    \"<span class=sf-dump-key>Other</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Other</span>\"\n                    \"<span class=sf-dump-key>Our Stores</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Our Stores</span>\"\n                    \"<span class=sf-dump-key>Out Of Stock</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Out Of Stock</span>\"\n                    \"<span class=sf-dump-key>Out of stock</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Out of stock</span>\"\n                    \"<span class=sf-dump-key>Over :fromPrice</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Over :fromPrice</span>\"\n                    \"<span class=sf-dump-key>Overview</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Overview</span>\"\n                    \"<span class=sf-dump-key>Owner</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Owner</span>\"\n                    \"<span class=sf-dump-key>PHP version :version required</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP version :version required</span>\"\n                    \"<span class=sf-dump-key>Page could not be found</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Page could not be found</span>\"\n                    \"<span class=sf-dump-key>Password</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Password</span>\"\n                    \"<span class=sf-dump-key>Password confirmation</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Password confirmation</span>\"\n                    \"<span class=sf-dump-key>PayPal ID</span>\" => \"<span class=sf-dump-str title=\"9 characters\">PayPal ID</span>\"\n                    \"<span class=sf-dump-key>PayPal ID is not set!</span>\" => \"<span class=sf-dump-str title=\"21 characters\">PayPal ID is not set!</span>\"\n                    \"<span class=sf-dump-key>PayPal automatically payout</span>\" => \"<span class=sf-dump-str title=\"27 characters\">PayPal automatically payout</span>\"\n                    \"<span class=sf-dump-key>PayPal payout info</span>\" => \"<span class=sf-dump-str title=\"18 characters\">PayPal payout info</span>\"\n                    \"<span class=sf-dump-key>Payment</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Payment</span>\"\n                    \"<span class=sf-dump-key>Payment Method</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Payment Method</span>\"\n                    \"<span class=sf-dump-key>Payment Type</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Payment Type</span>\"\n                    \"<span class=sf-dump-key>Payment description</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Payment description</span>\"\n                    \"<span class=sf-dump-key>Payment failed!</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Payment failed!</span>\"\n                    \"<span class=sf-dump-key>Payment failed! Missing transaction ID.</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Payment failed! Missing transaction ID.</span>\"\n                    \"<span class=sf-dump-key>Payment failed! Something wrong with your payment. Please try again.</span>\" => \"<span class=sf-dump-str title=\"68 characters\">Payment failed! Something wrong with your payment. Please try again.</span>\"\n                    \"<span class=sf-dump-key>Payment failed! Status: :status</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Payment failed! Status: :status</span>\"\n                    \"<span class=sf-dump-key>Payment method</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Payment method</span>\"\n                    \"<span class=sf-dump-key>Payment methods</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Payment methods</span>\"\n                    \"<span class=sf-dump-key>Payment status</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Payment status</span>\"\n                    \"<span class=sf-dump-key>Payment with :paymentType</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Payment with :paymentType</span>\"\n                    \"<span class=sf-dump-key>Payout account</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Payout account</span>\"\n                    \"<span class=sf-dump-key>Payout info</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Payout info</span>\"\n                    \"<span class=sf-dump-key>Payout method is not accepted!</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Payout method is not accepted!</span>\"\n                    \"<span class=sf-dump-key>Phone</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Phone</span>\"\n                    \"<span class=sf-dump-key>Phone (optional)</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Phone (optional)</span>\"\n                    \"<span class=sf-dump-key>Phone Number</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Phone Number</span>\"\n                    \"<span class=sf-dump-key>Phone number</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Phone number</span>\"\n                    \"<span class=sf-dump-key>Pinterest</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Pinterest</span>\"\n                    \"<span class=sf-dump-key>Please &lt;a href=&quot;:link&quot;&gt;login&lt;/a&gt; to write review!</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Please &lt;a href=&quot;:link&quot;&gt;login&lt;/a&gt; to write review!</span>\"\n                    \"<span class=sf-dump-key>Please agree to the terms and conditions before proceeding.</span>\" => \"<span class=sf-dump-str title=\"59 characters\">Please agree to the terms and conditions before proceeding.</span>\"\n                    \"<span class=sf-dump-key>Please fill out all shipping information to view available shipping methods!</span>\" => \"<span class=sf-dump-str title=\"76 characters\">Please fill out all shipping information to view available shipping methods!</span>\"\n                    \"<span class=sf-dump-key>Please provide a reason for the cancellation.</span>\" => \"<span class=sf-dump-str title=\"45 characters\">Please provide a reason for the cancellation.</span>\"\n                    \"<span class=sf-dump-key>Please purchase the product for a review!</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Please purchase the product for a review!</span>\"\n                    \"<span class=sf-dump-key>Please select at least 1 product to return!</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Please select at least 1 product to return!</span>\"\n                    \"<span class=sf-dump-key>Please select attributes</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Please select attributes</span>\"\n                    \"<span class=sf-dump-key>Please select product options!</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Please select product options!</span>\"\n                    \"<span class=sf-dump-key>Please solve the following math function: :label = ?</span>\" => \"<span class=sf-dump-str title=\"52 characters\">Please solve the following math function: :label = ?</span>\"\n                    \"<span class=sf-dump-key>Please switch currency to any supported currency</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Please switch currency to any supported currency</span>\"\n                    \"<span class=sf-dump-key>Please try again in a few minutes, or alternatively return to the homepage by &lt;a href=&quot;:link&quot;&gt;clicking here&lt;/a&gt;.</span>\" => \"<span class=sf-dump-str title=\"112 characters\">Please try again in a few minutes, or alternatively return to the homepage by &lt;a href=&quot;:link&quot;&gt;clicking here&lt;/a&gt;.</span>\"\n                    \"<span class=sf-dump-key>Please wait for the administrator to review and approve!</span>\" => \"<span class=sf-dump-str title=\"56 characters\">Please wait for the administrator to review and approve!</span>\"\n                    \"<span class=sf-dump-key>Plus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Plus</span>\"\n                    \"<span class=sf-dump-key>Popular</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Popular</span>\"\n                    \"<span class=sf-dump-key>Popular tags</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Popular tags</span>\"\n                    \"<span class=sf-dump-key>Popup Delay (seconds)</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Popup Delay (seconds)</span>\"\n                    \"<span class=sf-dump-key>Popup Description</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Popup Description</span>\"\n                    \"<span class=sf-dump-key>Popup Image</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Popup Image</span>\"\n                    \"<span class=sf-dump-key>Popup Subtitle</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Popup Subtitle</span>\"\n                    \"<span class=sf-dump-key>Popup Title</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Popup Title</span>\"\n                    \"<span class=sf-dump-key>Preferences</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Preferences</span>\"\n                    \"<span class=sf-dump-key>Preloader Version</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Preloader Version</span>\"\n                    \"<span class=sf-dump-key>Price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Price</span>\"\n                    \"<span class=sf-dump-key>Price: high to low</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Price: high to low</span>\"\n                    \"<span class=sf-dump-key>Price: low to high</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Price: low to high</span>\"\n                    \"<span class=sf-dump-key>Primary</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Primary</span>\"\n                    \"<span class=sf-dump-key>Primary color</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Primary color</span>\"\n                    \"<span class=sf-dump-key>Primary font</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Primary font</span>\"\n                    \"<span class=sf-dump-key>Print invoice</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Print invoice</span>\"\n                    \"<span class=sf-dump-key>Proceed to Checkout</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Proceed to Checkout</span>\"\n                    \"<span class=sf-dump-key>Proceed to checkout</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Proceed to checkout</span>\"\n                    \"<span class=sf-dump-key>Process payout</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Process payout</span>\"\n                    \"<span class=sf-dump-key>Processed PayPal payout successfully!</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Processed PayPal payout successfully!</span>\"\n                    \"<span class=sf-dump-key>Processing. Please wait...</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Processing. Please wait...</span>\"\n                    \"<span class=sf-dump-key>Product</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Product</span>\"\n                    \"<span class=sf-dump-key>Product :product is out of stock!</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Product :product is out of stock!</span>\"\n                    \"<span class=sf-dump-key>Product :product limited quantity allowed is :quantity</span>\" => \"<span class=sf-dump-str title=\"54 characters\">Product :product limited quantity allowed is :quantity</span>\"\n                    \"<span class=sf-dump-key>Product Categories</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Product Categories</span>\"\n                    \"<span class=sf-dump-key>Product Collections</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Product Collections</span>\"\n                    \"<span class=sf-dump-key>Product FAQs</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Product FAQs</span>\"\n                    \"<span class=sf-dump-key>Product ID is required</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Product ID is required</span>\"\n                    \"<span class=sf-dump-key>Product Name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Product Name</span>\"\n                    \"<span class=sf-dump-key>Product Reviews</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Product Reviews</span>\"\n                    \"<span class=sf-dump-key>Product categories</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Product categories</span>\"\n                    \"<span class=sf-dump-key>Product category products</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Product category products</span>\"\n                    \"<span class=sf-dump-key>Product feature icon :number</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Product feature icon :number</span>\"\n                    \"<span class=sf-dump-key>Product feature title :number</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Product feature title :number</span>\"\n                    \"<span class=sf-dump-key>Product features</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Product features</span>\"\n                    \"<span class=sf-dump-key>Product gallery image style</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Product gallery image style</span>\"\n                    \"<span class=sf-dump-key>Product gallery video position</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Product gallery video position</span>\"\n                    \"<span class=sf-dump-key>Product is not published yet.</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Product is not published yet.</span>\"\n                    \"<span class=sf-dump-key>Product is out of stock</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Product is out of stock</span>\"\n                    \"<span class=sf-dump-key>Product name &quot;:name&quot; does not exists</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Product name &quot;:name&quot; does not exists</span>\"\n                    \"<span class=sf-dump-key>Product sidebar</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Product sidebar</span>\"\n                    \"<span class=sf-dump-key>Product&#039;s name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Product&#039;s name</span>\"\n                    \"<span class=sf-dump-key>Product(s)</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Product(s)</span>\"\n                    \"<span class=sf-dump-key>Products</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Products</span>\"\n                    \"<span class=sf-dump-key>Products found</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Products found</span>\"\n                    \"<span class=sf-dump-key>Profile</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Profile</span>\"\n                    \"<span class=sf-dump-key>Promotion</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Promotion</span>\"\n                    \"<span class=sf-dump-key>Promotion discount amount</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Promotion discount amount</span>\"\n                    \"<span class=sf-dump-key>Public Key</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Public Key</span>\"\n                    \"<span class=sf-dump-key>Qty</span>\" => \"<span class=sf-dump-str title=\"3 characters\">Qty</span>\"\n                    \"<span class=sf-dump-key>Quantity</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Quantity</span>\"\n                    \"<span class=sf-dump-key>Quantity is required!</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Quantity is required!</span>\"\n                    \"<span class=sf-dump-key>Quantity must be a number!</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Quantity must be a number!</span>\"\n                    \"<span class=sf-dump-key>Questions and Answers</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Questions and Answers</span>\"\n                    \"<span class=sf-dump-key>Quick View</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Quick View</span>\"\n                    \"<span class=sf-dump-key>Rating</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Rating</span>\"\n                    \"<span class=sf-dump-key>Rating: high to low</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Rating: high to low</span>\"\n                    \"<span class=sf-dump-key>Rating: low to high</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Rating: low to high</span>\"\n                    \"<span class=sf-dump-key>Reason</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Reason</span>\"\n                    \"<span class=sf-dump-key>Reason (optional)</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Reason (optional)</span>\"\n                    \"<span class=sf-dump-key>Recent</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Recent</span>\"\n                    \"<span class=sf-dump-key>Recent Orders</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Recent Orders</span>\"\n                    \"<span class=sf-dump-key>Recent Posts</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Recent Posts</span>\"\n                    \"<span class=sf-dump-key>Recent posts</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Recent posts</span>\"\n                    \"<span class=sf-dump-key>Recent posts widget.</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Recent posts widget.</span>\"\n                    \"<span class=sf-dump-key>Recommended Items</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Recommended Items</span>\"\n                    \"<span class=sf-dump-key>Redirecting to Razorpay...</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Redirecting to Razorpay...</span>\"\n                    \"<span class=sf-dump-key>Refund amount</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Refund amount</span>\"\n                    \"<span class=sf-dump-key>Register</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Register</span>\"\n                    \"<span class=sf-dump-key>Register an account</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Register an account</span>\"\n                    \"<span class=sf-dump-key>Register an account on :name</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Register an account on :name</span>\"\n                    \"<span class=sf-dump-key>Register an account with above information?</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Register an account with above information?</span>\"\n                    \"<span class=sf-dump-key>Register as</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Register as</span>\"\n                    \"<span class=sf-dump-key>Register now</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Register now</span>\"\n                    \"<span class=sf-dump-key>Registered successfully!</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Registered successfully!</span>\"\n                    \"<span class=sf-dump-key>Related Posts</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Related Posts</span>\"\n                    \"<span class=sf-dump-key>Related products</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Related products</span>\"\n                    \"<span class=sf-dump-key>Remember me</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Remember me</span>\"\n                    \"<span class=sf-dump-key>Remove</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Remove</span>\"\n                    \"<span class=sf-dump-key>Remove image</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Remove image</span>\"\n                    \"<span class=sf-dump-key>Removed coupon :code successfully!</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Removed coupon :code successfully!</span>\"\n                    \"<span class=sf-dump-key>Removed item from cart successfully!</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Removed item from cart successfully!</span>\"\n                    \"<span class=sf-dump-key>Removed product :product from compare list successfully!</span>\" => \"<span class=sf-dump-str title=\"56 characters\">Removed product :product from compare list successfully!</span>\"\n                    \"<span class=sf-dump-key>Removed product :product from wishlist successfully!</span>\" => \"<span class=sf-dump-str title=\"52 characters\">Removed product :product from wishlist successfully!</span>\"\n                    \"<span class=sf-dump-key>Removing...</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Removing...</span>\"\n                    \"<span class=sf-dump-key>Request</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Request</span>\"\n                    \"<span class=sf-dump-key>Request Return Product(s)</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Request Return Product(s)</span>\"\n                    \"<span class=sf-dump-key>Request Return Product(s) In Order :id</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Request Return Product(s) In Order :id</span>\"\n                    \"<span class=sf-dump-key>Request delete account</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Request delete account</span>\"\n                    \"<span class=sf-dump-key>Request number</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Request number</span>\"\n                    \"<span class=sf-dump-key>Request return order with reason: :reason</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Request return order with reason: :reason</span>\"\n                    \"<span class=sf-dump-key>Requires company invoice (Please fill in your company information to receive the invoice)?</span>\" => \"<span class=sf-dump-str title=\"90 characters\">Requires company invoice (Please fill in your company information to receive the invoice)?</span>\"\n                    \"<span class=sf-dump-key>Reset Password</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Reset Password</span>\"\n                    \"<span class=sf-dump-key>Return Product(s)</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Return Product(s)</span>\"\n                    \"<span class=sf-dump-key>Return Reason</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Return Reason</span>\"\n                    \"<span class=sf-dump-key>Return items</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Return items</span>\"\n                    \"<span class=sf-dump-key>Returned Goods</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Returned Goods</span>\"\n                    \"<span class=sf-dump-key>Returned to Sender</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Returned to Sender</span>\"\n                    \"<span class=sf-dump-key>Revenue</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Revenue</span>\"\n                    \"<span class=sf-dump-key>Revenues</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Revenues</span>\"\n                    \"<span class=sf-dump-key>Revenues in :label</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Revenues in :label</span>\"\n                    \"<span class=sf-dump-key>Review</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Review</span>\"\n                    \"<span class=sf-dump-key>Review product &quot;:product&quot;</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Review product &quot;:product&quot;</span>\"\n                    \"<span class=sf-dump-key>Reviewed</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Reviewed</span>\"\n                    \"<span class=sf-dump-key>Reviews</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Reviews</span>\"\n                    \"<span class=sf-dump-key>Reviews :number</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Reviews :number</span>\"\n                    \"<span class=sf-dump-key>Run</span>\" => \"<span class=sf-dump-str title=\"3 characters\">Run</span>\"\n                    \"<span class=sf-dump-key>SKU</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SKU</span>\"\n                    \"<span class=sf-dump-key>SKU:</span>\" => \"<span class=sf-dump-str title=\"4 characters\">SKU:</span>\"\n                    \"<span class=sf-dump-key>Sales Reports</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Sales Reports</span>\"\n                    \"<span class=sf-dump-key>Same as shipping information</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Same as shipping information</span>\"\n                    \"<span class=sf-dump-key>Same fee :amount</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Same fee :amount</span>\"\n                    \"<span class=sf-dump-key>Save settings</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Save settings</span>\"\n                    \"<span class=sf-dump-key>Screenshot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Screenshot</span>\"\n                    \"<span class=sf-dump-key>Search</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Search</span>\"\n                    \"<span class=sf-dump-key>Search blog posts</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Search blog posts</span>\"\n                    \"<span class=sf-dump-key>Search for Products...</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Search for Products...</span>\"\n                    \"<span class=sf-dump-key>Search for...</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Search for...</span>\"\n                    \"<span class=sf-dump-key>Search in this store...</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Search in this store...</span>\"\n                    \"<span class=sf-dump-key>Search result for &quot;:query&quot;</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Search result for &quot;:query&quot;</span>\"\n                    \"<span class=sf-dump-key>Search result for: &quot;:query&quot;</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Search result for: &quot;:query&quot;</span>\"\n                    \"<span class=sf-dump-key>Search something...</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Search something...</span>\"\n                    \"<span class=sf-dump-key>Search vendor...</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Search vendor...</span>\"\n                    \"<span class=sf-dump-key>Search...</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Search...</span>\"\n                    \"<span class=sf-dump-key>Secondary color</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Secondary color</span>\"\n                    \"<span class=sf-dump-key>Seconds</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Seconds</span>\"\n                    \"<span class=sf-dump-key>Secret</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Secret</span>\"\n                    \"<span class=sf-dump-key>Secret Key</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Secret Key</span>\"\n                    \"<span class=sf-dump-key>See all results</span>\" => \"<span class=sf-dump-str title=\"15 characters\">See all results</span>\"\n                    \"<span class=sf-dump-key>Select</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Select</span>\"\n                    \"<span class=sf-dump-key>Select :name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Select :name</span>\"\n                    \"<span class=sf-dump-key>Select Options</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Select Options</span>\"\n                    \"<span class=sf-dump-key>Select a flash sale</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Select a flash sale</span>\"\n                    \"<span class=sf-dump-key>Select an option</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Select an option</span>\"\n                    \"<span class=sf-dump-key>Select available addresses</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Select available addresses</span>\"\n                    \"<span class=sf-dump-key>Select billing address...</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Select billing address...</span>\"\n                    \"<span class=sf-dump-key>Select categories</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Select categories</span>\"\n                    \"<span class=sf-dump-key>Select category</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Select category</span>\"\n                    \"<span class=sf-dump-key>Select city...</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Select city...</span>\"\n                    \"<span class=sf-dump-key>Select country...</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Select country...</span>\"\n                    \"<span class=sf-dump-key>Select file</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Select file</span>\"\n                    \"<span class=sf-dump-key>Select menu</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Select menu</span>\"\n                    \"<span class=sf-dump-key>Select state...</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Select state...</span>\"\n                    \"<span class=sf-dump-key>Select stores from the list</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Select stores from the list</span>\"\n                    \"<span class=sf-dump-key>Sell On Martfury</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Sell On Martfury</span>\"\n                    \"<span class=sf-dump-key>Sell on site text</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Sell on site text</span>\"\n                    \"<span class=sf-dump-key>Sell on site text (default: Sell on Martfury)</span>\" => \"<span class=sf-dump-str title=\"45 characters\">Sell on site text (default: Sell on Martfury)</span>\"\n                    \"<span class=sf-dump-key>Send</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Send</span>\"\n                    \"<span class=sf-dump-key>Send Password Reset Link</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Send Password Reset Link</span>\"\n                    \"<span class=sf-dump-key>Send mail with links to download apps</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Send mail with links to download apps</span>\"\n                    \"<span class=sf-dump-key>Send message</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Send message</span>\"\n                    \"<span class=sf-dump-key>Send message successfully!</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Send message successfully!</span>\"\n                    \"<span class=sf-dump-key>Sent at</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Sent at</span>\"\n                    \"<span class=sf-dump-key>Set the delay time to show the popup after the page is loaded. Set 0 to show the popup immediately.</span>\" => \"<span class=sf-dump-str title=\"99 characters\">Set the delay time to show the popup after the page is loaded. Set 0 to show the popup immediately.</span>\"\n                    \"<span class=sf-dump-key>Set the height of the logo in pixels. The default value is :default.</span>\" => \"<span class=sf-dump-str title=\"68 characters\">Set the height of the logo in pixels. The default value is :default.</span>\"\n                    \"<span class=sf-dump-key>Settings</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Settings</span>\"\n                    \"<span class=sf-dump-key>Setup license code</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Setup license code</span>\"\n                    \"<span class=sf-dump-key>Share on :social</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Share on :social</span>\"\n                    \"<span class=sf-dump-key>Share:</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Share:</span>\"\n                    \"<span class=sf-dump-key>Shipment status</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Shipment status</span>\"\n                    \"<span class=sf-dump-key>Shipments</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Shipments</span>\"\n                    \"<span class=sf-dump-key>Shipping Company Name</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Shipping Company Name</span>\"\n                    \"<span class=sf-dump-key>Shipping Information</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Shipping Information</span>\"\n                    \"<span class=sf-dump-key>Shipping Information:</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Shipping Information:</span>\"\n                    \"<span class=sf-dump-key>Shipping Label Created</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Shipping Label Created</span>\"\n                    \"<span class=sf-dump-key>Shipping Status</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Shipping Status</span>\"\n                    \"<span class=sf-dump-key>Shipping fee</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Shipping fee</span>\"\n                    \"<span class=sf-dump-key>Shipping fees not included</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Shipping fees not included</span>\"\n                    \"<span class=sf-dump-key>Shipping information</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Shipping information</span>\"\n                    \"<span class=sf-dump-key>Shipping method</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Shipping method</span>\"\n                    \"<span class=sf-dump-key>Shop Name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Shop Name</span>\"\n                    \"<span class=sf-dump-key>Shop Name is required.</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Shop Name is required.</span>\"\n                    \"<span class=sf-dump-key>Shop Phone</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Shop Phone</span>\"\n                    \"<span class=sf-dump-key>Shop Phone is required.</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Shop Phone is required.</span>\"\n                    \"<span class=sf-dump-key>Shop URL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Shop URL</span>\"\n                    \"<span class=sf-dump-key>Shop URL is existing. Please choose another one!</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Shop URL is existing. Please choose another one!</span>\"\n                    \"<span class=sf-dump-key>Shop URL is required.</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Shop URL is required.</span>\"\n                    \"<span class=sf-dump-key>Shop by Department</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Shop by Department</span>\"\n                    \"<span class=sf-dump-key>Shopping Cart</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Shopping Cart</span>\"\n                    \"<span class=sf-dump-key>Show</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Show</span>\"\n                    \"<span class=sf-dump-key>Show featured brands on the products page?</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Show featured brands on the products page?</span>\"\n                    \"<span class=sf-dump-key>Show recommend items on the products page?</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Show recommend items on the products page?</span>\"\n                    \"<span class=sf-dump-key>Site Copyright</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Site Copyright</span>\"\n                    \"<span class=sf-dump-key>Site features</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Site features</span>\"\n                    \"<span class=sf-dump-key>Slug</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Slug</span>\"\n                    \"<span class=sf-dump-key>Social</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Social</span>\"\n                    \"<span class=sf-dump-key>Social Links</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Social Links</span>\"\n                    \"<span class=sf-dump-key>Social Sharing</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Social Sharing</span>\"\n                    \"<span class=sf-dump-key>Social sharing buttons</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Social sharing buttons</span>\"\n                    \"<span class=sf-dump-key>Sold</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Sold</span>\"\n                    \"<span class=sf-dump-key>Sold By</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Sold By</span>\"\n                    \"<span class=sf-dump-key>Sold Items</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Sold Items</span>\"\n                    \"<span class=sf-dump-key>Sold by</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Sold by</span>\"\n                    \"<span class=sf-dump-key>Sold out</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Sold out</span>\"\n                    \"<span class=sf-dump-key>Something is broken. Please let us know what you were doing when this error occurred. We will fix it as soon as possible. Sorry for any inconvenience caused.</span>\" => \"<span class=sf-dump-str title=\"157 characters\">Something is broken. Please let us know what you were doing when this error occurred. We will fix it as soon as possible. Sorry for any inconvenience caused.</span>\"\n                    \"<span class=sf-dump-key>Something went wrong.</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Something went wrong.</span>\"\n                    \"<span class=sf-dump-key>Sorry, we are doing some maintenance. Please check back soon.</span>\" => \"<span class=sf-dump-str title=\"61 characters\">Sorry, we are doing some maintenance. Please check back soon.</span>\"\n                    \"<span class=sf-dump-key>Sorry, you can only order a maximum of :quantity units of :product at a time. Please adjust the quantity and try again.</span>\" => \"<span class=sf-dump-str title=\"119 characters\">Sorry, you can only order a maximum of :quantity units of :product at a time. Please adjust the quantity and try again.</span>\"\n                    \"<span class=sf-dump-key>Sort Items</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Sort Items</span>\"\n                    \"<span class=sf-dump-key>Specification</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Specification</span>\"\n                    \"<span class=sf-dump-key>Specification Attributes</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Specification Attributes</span>\"\n                    \"<span class=sf-dump-key>Specification Groups</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Specification Groups</span>\"\n                    \"<span class=sf-dump-key>Specification Tables</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Specification Tables</span>\"\n                    \"<span class=sf-dump-key>Square Logo</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Square Logo</span>\"\n                    \"<span class=sf-dump-key>Star</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Star</span>\"\n                    \"<span class=sf-dump-key>Start Shopping</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Start Shopping</span>\"\n                    \"<span class=sf-dump-key>Start shopping now</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Start shopping now</span>\"\n                    \"<span class=sf-dump-key>State</span>\" => \"<span class=sf-dump-str title=\"5 characters\">State</span>\"\n                    \"<span class=sf-dump-key>Status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Status</span>\"\n                    \"<span class=sf-dump-key>Store ID</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Store ID</span>\"\n                    \"<span class=sf-dump-key>Store Information</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Store Information</span>\"\n                    \"<span class=sf-dump-key>Store Name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Store Name</span>\"\n                    \"<span class=sf-dump-key>Store Password (API/Secret key)</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Store Password (API/Secret key)</span>\"\n                    \"<span class=sf-dump-key>Store URL</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Store URL</span>\"\n                    \"<span class=sf-dump-key>Stores</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Stores</span>\"\n                    \"<span class=sf-dump-key>Style</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Style</span>\"\n                    \"<span class=sf-dump-key>Style of theme</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Style of theme</span>\"\n                    \"<span class=sf-dump-key>Sub Total</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Sub Total</span>\"\n                    \"<span class=sf-dump-key>Sub amount</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Sub amount</span>\"\n                    \"<span class=sf-dump-key>Subject</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Subject</span>\"\n                    \"<span class=sf-dump-key>Submit</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Submit</span>\"\n                    \"<span class=sf-dump-key>Submit Return Request</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Submit Return Request</span>\"\n                    \"<span class=sf-dump-key>Submit Review</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Submit Review</span>\"\n                    \"<span class=sf-dump-key>Subscribe</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Subscribe</span>\"\n                    \"<span class=sf-dump-key>Subscribe to newsletter successfully!</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Subscribe to newsletter successfully!</span>\"\n                    \"<span class=sf-dump-key>Subscribe to the mailing list to receive updates on new arrivals, special offers and our promotions.</span>\" => \"<span class=sf-dump-str title=\"100 characters\">Subscribe to the mailing list to receive updates on new arrivals, special offers and our promotions.</span>\"\n                    \"<span class=sf-dump-key>Subtitle</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Subtitle</span>\"\n                    \"<span class=sf-dump-key>Subtitle :number</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Subtitle :number</span>\"\n                    \"<span class=sf-dump-key>Subtotal</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Subtotal</span>\"\n                    \"<span class=sf-dump-key>Success</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Success</span>\"\n                    \"<span class=sf-dump-key>Support native audio</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Support native audio</span>\"\n                    \"<span class=sf-dump-key>Support native video, YouTube, Vimeo, TikTok, X (Twitter)</span>\" => \"<span class=sf-dump-str title=\"57 characters\">Support native video, YouTube, Vimeo, TikTok, X (Twitter)</span>\"\n                    \"<span class=sf-dump-key>Tab #:number</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Tab #:number</span>\"\n                    \"<span class=sf-dump-key>Tablet Image</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Tablet Image</span>\"\n                    \"<span class=sf-dump-key>Tags</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Tags</span>\"\n                    \"<span class=sf-dump-key>Take me home</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Take me home</span>\"\n                    \"<span class=sf-dump-key>Tax</span>\" => \"<span class=sf-dump-str title=\"3 characters\">Tax</span>\"\n                    \"<span class=sf-dump-key>Tax ID</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Tax ID</span>\"\n                    \"<span class=sf-dump-key>Tax ID:</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Tax ID:</span>\"\n                    \"<span class=sf-dump-key>Tax info</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Tax info</span>\"\n                    \"<span class=sf-dump-key>Tax information</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Tax information</span>\"\n                    \"<span class=sf-dump-key>Telegram</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Telegram</span>\"\n                    \"<span class=sf-dump-key>Temporarily down for maintenance</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Temporarily down for maintenance</span>\"\n                    \"<span class=sf-dump-key>Term and Policy</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Term and Policy</span>\"\n                    \"<span class=sf-dump-key>Terms and Privacy Policy</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Terms and Privacy Policy</span>\"\n                    \"<span class=sf-dump-key>Thank you for purchasing our products!</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Thank you for purchasing our products!</span>\"\n                    \"<span class=sf-dump-key>The .env file is not writable.</span>\" => \"<span class=sf-dump-str title=\"30 characters\">The .env file is not writable.</span>\"\n                    \"<span class=sf-dump-key>The balance is not enough for withdrawal</span>\" => \"<span class=sf-dump-str title=\"40 characters\">The balance is not enough for withdrawal</span>\"\n                    \"<span class=sf-dump-key>The debug mode has been disabled successfully.</span>\" => \"<span class=sf-dump-str title=\"46 characters\">The debug mode has been disabled successfully.</span>\"\n                    \"<span class=sf-dump-key>The debug mode is already disabled.</span>\" => \"<span class=sf-dump-str title=\"35 characters\">The debug mode is already disabled.</span>\"\n                    \"<span class=sf-dump-key>The font size in pixels (px). Default is :default</span>\" => \"<span class=sf-dump-str title=\"49 characters\">The font size in pixels (px). Default is :default</span>\"\n                    \"<span class=sf-dump-key>The given email address has not been confirmed. &lt;a href=&quot;:resend_link&quot;&gt;Resend confirmation link.&lt;/a&gt;</span>\" => \"<span class=sf-dump-str title=\"100 characters\">The given email address has not been confirmed. &lt;a href=&quot;:resend_link&quot;&gt;Resend confirmation link.&lt;/a&gt;</span>\"\n                    \"<span class=sf-dump-key>The minimum withdrawal amount is :amount</span>\" => \"<span class=sf-dump-str title=\"40 characters\">The minimum withdrawal amount is :amount</span>\"\n                    \"<span class=sf-dump-key>The order could not be found. Please try again or contact us if you need assistance.</span>\" => \"<span class=sf-dump-str title=\"84 characters\">The order could not be found. Please try again or contact us if you need assistance.</span>\"\n                    \"<span class=sf-dump-key>The order is currently being processed. For expedited processing, kindly upload a copy of your payment proof:</span>\" => \"<span class=sf-dump-str title=\"109 characters\">The order is currently being processed. For expedited processing, kindly upload a copy of your payment proof:</span>\"\n                    \"<span class=sf-dump-key>The page you are looking for could not be found.</span>\" => \"<span class=sf-dump-str title=\"48 characters\">The page you are looking for could not be found.</span>\"\n                    \"<span class=sf-dump-key>The selected :attribute is invalid.</span>\" => \"<span class=sf-dump-str title=\"35 characters\">The selected :attribute is invalid.</span>\"\n                    \"<span class=sf-dump-key>The system is up-to-date. There are no new versions to update!</span>\" => \"<span class=sf-dump-str title=\"62 characters\">The system is up-to-date. There are no new versions to update!</span>\"\n                    \"<span class=sf-dump-key>Theme ads</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Theme ads</span>\"\n                    \"<span class=sf-dump-key>Theme emails</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Theme emails</span>\"\n                    \"<span class=sf-dump-key>Theme options</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Theme options</span>\"\n                    \"<span class=sf-dump-key>There is no data to display!</span>\" => \"<span class=sf-dump-str title=\"28 characters\">There is no data to display!</span>\"\n                    \"<span class=sf-dump-key>This action will permanently delete your account and all associated data and irreversible. Please be sure before proceeding.</span>\" => \"<span class=sf-dump-str title=\"124 characters\">This action will permanently delete your account and all associated data and irreversible. Please be sure before proceeding.</span>\"\n                    \"<span class=sf-dump-key>This credential is invalid Google Analytics credentials.</span>\" => \"<span class=sf-dump-str title=\"56 characters\">This credential is invalid Google Analytics credentials.</span>\"\n                    \"<span class=sf-dump-key>This feature is temporary disabled in demo mode. Please use another login option. Such as Google.</span>\" => \"<span class=sf-dump-str title=\"97 characters\">This feature is temporary disabled in demo mode. Please use another login option. Such as Google.</span>\"\n                    \"<span class=sf-dump-key>This file is not a valid JSON file.</span>\" => \"<span class=sf-dump-str title=\"35 characters\">This file is not a valid JSON file.</span>\"\n                    \"<span class=sf-dump-key>This image will be used as placeholder for lazy load images. The best size for this image is 250x250px.</span>\" => \"<span class=sf-dump-str title=\"103 characters\">This image will be used as placeholder for lazy load images. The best size for this image is 250x250px.</span>\"\n                    \"<span class=sf-dump-key>This logo will be used in some special cases. Such as checkout page.</span>\" => \"<span class=sf-dump-str title=\"68 characters\">This logo will be used in some special cases. Such as checkout page.</span>\"\n                    \"<span class=sf-dump-key>This product is not available.</span>\" => \"<span class=sf-dump-str title=\"30 characters\">This product is not available.</span>\"\n                    \"<span class=sf-dump-key>This product is out of stock or not exists!</span>\" => \"<span class=sf-dump-str title=\"43 characters\">This product is out of stock or not exists!</span>\"\n                    \"<span class=sf-dump-key>Time</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Time</span>\"\n                    \"<span class=sf-dump-key>Times downloaded</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Times downloaded</span>\"\n                    \"<span class=sf-dump-key>Title</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Title</span>\"\n                    \"<span class=sf-dump-key>Title :number</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Title :number</span>\"\n                    \"<span class=sf-dump-key>To show chat box on that website, please go to :link and add :domain to whitelist domains!</span>\" => \"<span class=sf-dump-str title=\"90 characters\">To show chat box on that website, please go to :link and add :domain to whitelist domains!</span>\"\n                    \"<span class=sf-dump-key>Top</span>\" => \"<span class=sf-dump-str title=\"3 characters\">Top</span>\"\n                    \"<span class=sf-dump-key>Top Selling Products</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Top Selling Products</span>\"\n                    \"<span class=sf-dump-key>Top Slider Image 1 (deprecated)</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Top Slider Image 1 (deprecated)</span>\"\n                    \"<span class=sf-dump-key>Top Slider Image 2 (deprecated)</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Top Slider Image 2 (deprecated)</span>\"\n                    \"<span class=sf-dump-key>Total</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Total</span>\"\n                    \"<span class=sf-dump-key>Total Amount</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Total Amount</span>\"\n                    \"<span class=sf-dump-key>Total amount</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Total amount</span>\"\n                    \"<span class=sf-dump-key>Track</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Track</span>\"\n                    \"<span class=sf-dump-key>Track your order</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Track your order</span>\"\n                    \"<span class=sf-dump-key>Tracking ID</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Tracking ID</span>\"\n                    \"<span class=sf-dump-key>Tracking Link</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Tracking Link</span>\"\n                    \"<span class=sf-dump-key>Tracking your order status</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Tracking your order status</span>\"\n                    \"<span class=sf-dump-key>Transaction ID</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Transaction ID</span>\"\n                    \"<span class=sf-dump-key>Transaction is already successfully completed!</span>\" => \"<span class=sf-dump-str title=\"46 characters\">Transaction is already successfully completed!</span>\"\n                    \"<span class=sf-dump-key>Transaction is successfully completed!</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Transaction is successfully completed!</span>\"\n                    \"<span class=sf-dump-key>Trending Products</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Trending Products</span>\"\n                    \"<span class=sf-dump-key>Type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Type</span>\"\n                    \"<span class=sf-dump-key>Type your message...</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Type your message...</span>\"\n                    \"<span class=sf-dump-key>UPI ID</span>\" => \"<span class=sf-dump-str title=\"6 characters\">UPI ID</span>\"\n                    \"<span class=sf-dump-key>URL</span>\" => \"<span class=sf-dump-str title=\"3 characters\">URL</span>\"\n                    \"<span class=sf-dump-key>Unable to set debug mode. No APP_DEBUG variable was found in the .env file.</span>\" => \"<span class=sf-dump-str title=\"75 characters\">Unable to set debug mode. No APP_DEBUG variable was found in the .env file.</span>\"\n                    \"<span class=sf-dump-key>Unknown</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Unknown</span>\"\n                    \"<span class=sf-dump-key>Unlimited by default</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Unlimited by default</span>\"\n                    \"<span class=sf-dump-key>Unsubscribe to newsletter successfully</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Unsubscribe to newsletter successfully</span>\"\n                    \"<span class=sf-dump-key>Update</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Update</span>\"\n                    \"<span class=sf-dump-key>Update :name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Update :name</span>\"\n                    \"<span class=sf-dump-key>Update cart successfully!</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Update cart successfully!</span>\"\n                    \"<span class=sf-dump-key>Update profile successfully!</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Update profile successfully!</span>\"\n                    \"<span class=sf-dump-key>Update return order status to: :status</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Update return order status to: :status</span>\"\n                    \"<span class=sf-dump-key>Update successfully!</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Update successfully!</span>\"\n                    \"<span class=sf-dump-key>Update withdrawal request #:id</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Update withdrawal request #:id</span>\"\n                    \"<span class=sf-dump-key>Updated avatar successfully!</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Updated avatar successfully!</span>\"\n                    \"<span class=sf-dump-key>Updated registration info successfully!</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Updated registration info successfully!</span>\"\n                    \"<span class=sf-dump-key>Upload</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Upload</span>\"\n                    \"<span class=sf-dump-key>Upload Service Account JSON File</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Upload Service Account JSON File</span>\"\n                    \"<span class=sf-dump-key>Upload photos</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Upload photos</span>\"\n                    \"<span class=sf-dump-key>Uploaded Certificate</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Uploaded Certificate</span>\"\n                    \"<span class=sf-dump-key>Uploaded Government ID</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Uploaded Government ID</span>\"\n                    \"<span class=sf-dump-key>Uploaded proof successfully</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Uploaded proof successfully</span>\"\n                    \"<span class=sf-dump-key>Use this address as default.</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Use this address as default.</span>\"\n                    \"<span class=sf-dump-key>Using coupon code</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Using coupon code</span>\"\n                    \"<span class=sf-dump-key>Validation Fail!</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Validation Fail!</span>\"\n                    \"<span class=sf-dump-key>Variables</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Variables</span>\"\n                    \"<span class=sf-dump-key>Vendor</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Vendor</span>\"\n                    \"<span class=sf-dump-key>Vendor Dashboard</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Vendor Dashboard</span>\"\n                    \"<span class=sf-dump-key>Vendor account is not verified.</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Vendor account is not verified.</span>\"\n                    \"<span class=sf-dump-key>Vendor:</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Vendor:</span>\"\n                    \"<span class=sf-dump-key>Vertical</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Vertical</span>\"\n                    \"<span class=sf-dump-key>View</span>\" => \"<span class=sf-dump-str title=\"4 characters\">View</span>\"\n                    \"<span class=sf-dump-key>View All</span>\" => \"<span class=sf-dump-str title=\"8 characters\">View All</span>\"\n                    \"<span class=sf-dump-key>View Cart</span>\" => \"<span class=sf-dump-str title=\"9 characters\">View Cart</span>\"\n                    \"<span class=sf-dump-key>View Certificate</span>\" => \"<span class=sf-dump-str title=\"16 characters\">View Certificate</span>\"\n                    \"<span class=sf-dump-key>View Full Orders</span>\" => \"<span class=sf-dump-str title=\"16 characters\">View Full Orders</span>\"\n                    \"<span class=sf-dump-key>View Full Products</span>\" => \"<span class=sf-dump-str title=\"18 characters\">View Full Products</span>\"\n                    \"<span class=sf-dump-key>View Government ID</span>\" => \"<span class=sf-dump-str title=\"18 characters\">View Government ID</span>\"\n                    \"<span class=sf-dump-key>View Receipt:</span>\" => \"<span class=sf-dump-str title=\"13 characters\">View Receipt:</span>\"\n                    \"<span class=sf-dump-key>View all results</span>\" => \"<span class=sf-dump-str title=\"16 characters\">View all results</span>\"\n                    \"<span class=sf-dump-key>View full details</span>\" => \"<span class=sf-dump-str title=\"17 characters\">View full details</span>\"\n                    \"<span class=sf-dump-key>View withdrawal request #:id</span>\" => \"<span class=sf-dump-str title=\"28 characters\">View withdrawal request #:id</span>\"\n                    \"<span class=sf-dump-key>View your ads.txt here: :url</span>\" => \"<span class=sf-dump-str title=\"28 characters\">View your ads.txt here: :url</span>\"\n                    \"<span class=sf-dump-key>View your store</span>\" => \"<span class=sf-dump-str title=\"15 characters\">View your store</span>\"\n                    \"<span class=sf-dump-key>View your store &lt;a href=&quot;:url&quot;&gt;here&lt;/a&gt;</span>\" => \"<span class=sf-dump-str title=\"39 characters\">View your store &lt;a href=&quot;:url&quot;&gt;here&lt;/a&gt;</span>\"\n                    \"<span class=sf-dump-key>Viewing message #:id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Viewing message #:id</span>\"\n                    \"<span class=sf-dump-key>Visit Store</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Visit Store</span>\"\n                    \"<span class=sf-dump-key>Waiting for approval</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Waiting for approval</span>\"\n                    \"<span class=sf-dump-key>Waiting for your review</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Waiting for your review</span>\"\n                    \"<span class=sf-dump-key>Warning: This product is on backorder and may take longer to ship.</span>\" => \"<span class=sf-dump-str title=\"66 characters\">Warning: This product is on backorder and may take longer to ship.</span>\"\n                    \"<span class=sf-dump-key>We Using Safe Payment For</span>\" => \"<span class=sf-dump-str title=\"25 characters\">We Using Safe Payment For</span>\"\n                    \"<span class=sf-dump-key>We have sent you an email to verify your email. Please check and confirm your email address!</span>\" => \"<span class=sf-dump-str title=\"92 characters\">We have sent you an email to verify your email. Please check and confirm your email address!</span>\"\n                    \"<span class=sf-dump-key>We sent an email with download links to your email, please check it!</span>\" => \"<span class=sf-dump-str title=\"68 characters\">We sent an email with download links to your email, please check it!</span>\"\n                    \"<span class=sf-dump-key>We sent you another confirmation email. You should receive it shortly.</span>\" => \"<span class=sf-dump-str title=\"70 characters\">We sent you another confirmation email. You should receive it shortly.</span>\"\n                    \"<span class=sf-dump-key>We will send you an email to confirm your account deletion. Once you confirm, your account will be deleted permanently.</span>\" => \"<span class=sf-dump-str title=\"119 characters\">We will send you an email to confirm your account deletion. Once you confirm, your account will be deleted permanently.</span>\"\n                    \"<span class=sf-dump-key>WhatsApp</span>\" => \"<span class=sf-dump-str title=\"8 characters\">WhatsApp</span>\"\n                    \"<span class=sf-dump-key>When enabled, shortcode content will be loaded sequentially as the page loads, rather than all at once. This can help improve page load times.</span>\" => \"<span class=sf-dump-str title=\"142 characters\">When enabled, shortcode content will be loaded sequentially as the page loads, rather than all at once. This can help improve page load times.</span>\"\n                    \"<span class=sf-dump-key>Widget display blog categories</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Widget display blog categories</span>\"\n                    \"<span class=sf-dump-key>Widgets in bottom footer</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Widgets in bottom footer</span>\"\n                    \"<span class=sf-dump-key>Widgets in footer of page</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Widgets in footer of page</span>\"\n                    \"<span class=sf-dump-key>Width</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Width</span>\"\n                    \"<span class=sf-dump-key>Wishlist</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Wishlist</span>\"\n                    \"<span class=sf-dump-key>Withdrawal images</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Withdrawal images</span>\"\n                    \"<span class=sf-dump-key>Withdrawal request</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Withdrawal request</span>\"\n                    \"<span class=sf-dump-key>Withdrawals</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Withdrawals</span>\"\n                    \"<span class=sf-dump-key>Write your message here</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Write your message here</span>\"\n                    \"<span class=sf-dump-key>Write your review</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Write your review</span>\"\n                    \"<span class=sf-dump-key>X (Twitter)</span>\" => \"<span class=sf-dump-str title=\"11 characters\">X (Twitter)</span>\"\n                    \"<span class=sf-dump-key>Yes</span>\" => \"<span class=sf-dump-str title=\"3 characters\">Yes</span>\"\n                    \"<span class=sf-dump-key>Yes, turn off</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Yes, turn off</span>\"\n                    \"<span class=sf-dump-key>You can change it &lt;a href=&quot;:link&quot;&gt;here&lt;/a&gt;</span>\" => \"<span class=sf-dump-str title=\"42 characters\">You can change it &lt;a href=&quot;:link&quot;&gt;here&lt;/a&gt;</span>\"\n                    \"<span class=sf-dump-key>You can create your app in :link</span>\" => \"<span class=sf-dump-str title=\"32 characters\">You can create your app in :link</span>\"\n                    \"<span class=sf-dump-key>You can get fan page ID using this site :link</span>\" => \"<span class=sf-dump-str title=\"45 characters\">You can get fan page ID using this site :link</span>\"\n                    \"<span class=sf-dump-key>You can now download it by clicking the links below</span>\" => \"<span class=sf-dump-str title=\"51 characters\">You can now download it by clicking the links below</span>\"\n                    \"<span class=sf-dump-key>You can only add products from the same store to the cart.</span>\" => \"<span class=sf-dump-str title=\"58 characters\">You can only add products from the same store to the cart.</span>\"\n                    \"<span class=sf-dump-key>You can upload the following file types: jpg, jpeg, png, pdf and max file size is 2MB.</span>\" => \"<span class=sf-dump-str title=\"86 characters\">You can upload the following file types: jpg, jpeg, png, pdf and max file size is 2MB.</span>\"\n                    \"<span class=sf-dump-key>You can upload up to :total photos, each photo maximum size is :max MB.</span>\" => \"<span class=sf-dump-str title=\"71 characters\">You can upload up to :total photos, each photo maximum size is :max MB.</span>\"\n                    \"<span class=sf-dump-key>You can upload up to :total photos, each photo maximum size is :max kilobytes</span>\" => \"<span class=sf-dump-str title=\"77 characters\">You can upload up to :total photos, each photo maximum size is :max kilobytes</span>\"\n                    \"<span class=sf-dump-key>You cannot send a message to your own store.</span>\" => \"<span class=sf-dump-str title=\"44 characters\">You cannot send a message to your own store.</span>\"\n                    \"<span class=sf-dump-key>You do not have any products to review yet. Just shopping!</span>\" => \"<span class=sf-dump-str title=\"58 characters\">You do not have any products to review yet. Just shopping!</span>\"\n                    \"<span class=sf-dump-key>You have :total product(s) but no orders yet</span>\" => \"<span class=sf-dump-str title=\"44 characters\">You have :total product(s) but no orders yet</span>\"\n                    \"<span class=sf-dump-key>You have a coupon code?</span>\" => \"<span class=sf-dump-str title=\"23 characters\">You have a coupon code?</span>\"\n                    \"<span class=sf-dump-key>You have created a payment #:charge_id via :channel :time : :amount</span>\" => \"<span class=sf-dump-str title=\"67 characters\">You have created a payment #:charge_id via :channel :time : :amount</span>\"\n                    \"<span class=sf-dump-key>You have money!</span>\" => \"<span class=sf-dump-str title=\"15 characters\">You have money!</span>\"\n                    \"<span class=sf-dump-key>You have not added any addresses yet.</span>\" => \"<span class=sf-dump-str title=\"37 characters\">You have not added any addresses yet.</span>\"\n                    \"<span class=sf-dump-key>You have not placed any order return requests yet.</span>\" => \"<span class=sf-dump-str title=\"50 characters\">You have not placed any order return requests yet.</span>\"\n                    \"<span class=sf-dump-key>You have not placed any orders yet.</span>\" => \"<span class=sf-dump-str title=\"35 characters\">You have not placed any orders yet.</span>\"\n                    \"<span class=sf-dump-key>You have not purchased any digital products yet.</span>\" => \"<span class=sf-dump-str title=\"48 characters\">You have not purchased any digital products yet.</span>\"\n                    \"<span class=sf-dump-key>You have not reviewed any products yet.</span>\" => \"<span class=sf-dump-str title=\"39 characters\">You have not reviewed any products yet.</span>\"\n                    \"<span class=sf-dump-key>You have recovered from previous orders!</span>\" => \"<span class=sf-dump-str title=\"40 characters\">You have recovered from previous orders!</span>\"\n                    \"<span class=sf-dump-key>You have reviewed this product already!</span>\" => \"<span class=sf-dump-str title=\"39 characters\">You have reviewed this product already!</span>\"\n                    \"<span class=sf-dump-key>You have uploaded a copy of your payment proof.</span>\" => \"<span class=sf-dump-str title=\"47 characters\">You have uploaded a copy of your payment proof.</span>\"\n                    \"<span class=sf-dump-key>You must agree to the terms and conditions and privacy policy.</span>\" => \"<span class=sf-dump-str title=\"62 characters\">You must agree to the terms and conditions and privacy policy.</span>\"\n                    \"<span class=sf-dump-key>You need to add :quantity more items to place your order.</span>\" => \"<span class=sf-dump-str title=\"58 characters\">You need to add :quantity more items to place your order. </span>\"\n                    \"<span class=sf-dump-key>You received a payment. Thanks for selling on our site!</span>\" => \"<span class=sf-dump-str title=\"55 characters\">You received a payment. Thanks for selling on our site!</span>\"\n                    \"<span class=sf-dump-key>You successfully confirmed your email address.</span>\" => \"<span class=sf-dump-str title=\"46 characters\">You successfully confirmed your email address.</span>\"\n                    \"<span class=sf-dump-key>You will be redirected to :name to complete the payment.</span>\" => \"<span class=sf-dump-str title=\"56 characters\">You will be redirected to :name to complete the payment.</span>\"\n                    \"<span class=sf-dump-key>You will receive money through the information below</span>\" => \"<span class=sf-dump-str title=\"52 characters\">You will receive money through the information below</span>\"\n                    \"<span class=sf-dump-key>YouTube URL</span>\" => \"<span class=sf-dump-str title=\"11 characters\">YouTube URL</span>\"\n                    \"<span class=sf-dump-key>YouTube video</span>\" => \"<span class=sf-dump-str title=\"13 characters\">YouTube video</span>\"\n                    \"<span class=sf-dump-key>YouTube, Vimeo, TikTok, ...</span>\" => \"<span class=sf-dump-str title=\"27 characters\">YouTube, Vimeo, TikTok, ...</span>\"\n                    \"<span class=sf-dump-key>Your Address</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Your Address</span>\"\n                    \"<span class=sf-dump-key>Your Email</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Your Email</span>\"\n                    \"<span class=sf-dump-key>Your Google Adsense ads.txt</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Your Google Adsense ads.txt</span>\"\n                    \"<span class=sf-dump-key>Your Name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Your Name</span>\"\n                    \"<span class=sf-dump-key>Your Phone</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Your Phone</span>\"\n                    \"<span class=sf-dump-key>Your account has been locked, please contact the administrator.</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Your account has been locked, please contact the administrator.</span>\"\n                    \"<span class=sf-dump-key>Your asset files have been published successfully.</span>\" => \"<span class=sf-dump-str title=\"50 characters\">Your asset files have been published successfully.</span>\"\n                    \"<span class=sf-dump-key>Your cart is empty</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Your cart is empty</span>\"\n                    \"<span class=sf-dump-key>Your cart is empty!</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Your cart is empty!</span>\"\n                    \"<span class=sf-dump-key>Your compare list is empty</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Your compare list is empty</span>\"\n                    \"<span class=sf-dump-key>Your email</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Your email</span>\"\n                    \"<span class=sf-dump-key>Your email address</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Your email address</span>\"\n                    \"<span class=sf-dump-key>Your email address will not be published. Required fields are marked *</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Your email address will not be published. Required fields are marked *</span>\"\n                    \"<span class=sf-dump-key>Your email does not exist in the system or you have unsubscribed already!</span>\" => \"<span class=sf-dump-str title=\"73 characters\">Your email does not exist in the system or you have unsubscribed already!</span>\"\n                    \"<span class=sf-dump-key>Your email is in blacklist. Please use another email address.</span>\" => \"<span class=sf-dump-str title=\"61 characters\">Your email is in blacklist. Please use another email address.</span>\"\n                    \"<span class=sf-dump-key>Your full name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Your full name</span>\"\n                    \"<span class=sf-dump-key>Your message contains blacklist words: &quot;:words&quot;.</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Your message contains blacklist words: &quot;:words&quot;.</span>\"\n                    \"<span class=sf-dump-key>Your name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Your name</span>\"\n                    \"<span class=sf-dump-key>Your order is successfully placed</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Your order is successfully placed</span>\"\n                    \"<span class=sf-dump-key>Your personal data will be used to support your experience throughout this website, to manage access to your account.</span>\" => \"<span class=sf-dump-str title=\"117 characters\">Your personal data will be used to support your experience throughout this website, to manage access to your account.</span>\"\n                    \"<span class=sf-dump-key>Your rating:</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Your rating:</span>\"\n                    \"<span class=sf-dump-key>Your shopping cart has digital product(s), so you need to sign in to continue!</span>\" => \"<span class=sf-dump-str title=\"78 characters\">Your shopping cart has digital product(s), so you need to sign in to continue!</span>\"\n                    \"<span class=sf-dump-key>Your system has been cleaned up successfully.</span>\" => \"<span class=sf-dump-str title=\"45 characters\">Your system has been cleaned up successfully.</span>\"\n                    \"<span class=sf-dump-key>Your wishlist list is empty</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Your wishlist list is empty</span>\"\n                    \"<span class=sf-dump-key>Zip code</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Zip code</span>\"\n                    \"<span class=sf-dump-key>Zipcode</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Zipcode</span>\"\n                    \"<span class=sf-dump-key>billion</span>\" => \"<span class=sf-dump-str title=\"7 characters\">billion</span>\"\n                    \"<span class=sf-dump-key>by</span>\" => \"<span class=sf-dump-str title=\"2 characters\">by</span>\"\n                    \"<span class=sf-dump-key>centimeters</span>\" => \"<span class=sf-dump-str title=\"11 characters\">centimeters</span>\"\n                    \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n                    \"<span class=sf-dump-key>for all orders</span>\" => \"<span class=sf-dump-str title=\"14 characters\">for all orders</span>\"\n                    \"<span class=sf-dump-key>for all product in collection :collections</span>\" => \"<span class=sf-dump-str title=\"42 characters\">for all product in collection :collections</span>\"\n                    \"<span class=sf-dump-key>for all products in category :categories</span>\" => \"<span class=sf-dump-str title=\"40 characters\">for all products in category :categories</span>\"\n                    \"<span class=sf-dump-key>for all products in collection :collections</span>\" => \"<span class=sf-dump-str title=\"43 characters\">for all products in collection :collections</span>\"\n                    \"<span class=sf-dump-key>for all products in order</span>\" => \"<span class=sf-dump-str title=\"25 characters\">for all products in order</span>\"\n                    \"<span class=sf-dump-key>for customer(s) :customers</span>\" => \"<span class=sf-dump-str title=\"26 characters\">for customer(s) :customers</span>\"\n                    \"<span class=sf-dump-key>for order with amount from :price</span>\" => \"<span class=sf-dump-str title=\"33 characters\">for order with amount from :price</span>\"\n                    \"<span class=sf-dump-key>for product variant(s) :variants</span>\" => \"<span class=sf-dump-str title=\"32 characters\">for product variant(s) :variants</span>\"\n                    \"<span class=sf-dump-key>for product(s) :products</span>\" => \"<span class=sf-dump-str title=\"24 characters\">for product(s) :products</span>\"\n                    \"<span class=sf-dump-key>grams</span>\" => \"<span class=sf-dump-str title=\"5 characters\">grams</span>\"\n                    \"<span class=sf-dump-key>here</span>\" => \"<span class=sf-dump-str title=\"4 characters\">here</span>\"\n                    \"<span class=sf-dump-key>iOS app URL</span>\" => \"<span class=sf-dump-str title=\"11 characters\">iOS app URL</span>\"\n                    \"<span class=sf-dump-key>in</span>\" => \"<span class=sf-dump-str title=\"2 characters\">in</span>\"\n                    \"<span class=sf-dump-key>kilograms</span>\" => \"<span class=sf-dump-str title=\"9 characters\">kilograms</span>\"\n                    \"<span class=sf-dump-key>limited to use coupon code per customer. This coupon can only be used once per customer!</span>\" => \"<span class=sf-dump-str title=\"88 characters\">limited to use coupon code per customer. This coupon can only be used once per customer!</span>\"\n                    \"<span class=sf-dump-key>meters</span>\" => \"<span class=sf-dump-str title=\"6 characters\">meters</span>\"\n                    \"<span class=sf-dump-key>million</span>\" => \"<span class=sf-dump-str title=\"7 characters\">million</span>\"\n                    \"<span class=sf-dump-key>reviews</span>\" => \"<span class=sf-dump-str title=\"7 characters\">reviews</span>\"\n                    \"<span class=sf-dump-key>show less</span>\" => \"<span class=sf-dump-str title=\"9 characters\">show less</span>\"\n                    \"<span class=sf-dump-key>show more</span>\" => \"<span class=sf-dump-str title=\"9 characters\">show more</span>\"\n                    \"<span class=sf-dump-key>when shipping fee less than or equal :amount</span>\" => \"<span class=sf-dump-str title=\"44 characters\">when shipping fee less than or equal :amount</span>\"\n                    \"<span class=sf-dump-key>| (pipe)</span>\" => \"<span class=sf-dump-str title=\"8 characters\">| (pipe)</span>\"\n                    \"<span class=sf-dump-key>&#9989; Purchased :time</span>\" => \"<span class=sf-dump-str title=\"17 characters\">&#9989; Purchased :time</span>\"\n                  </samp>]\n                </samp>]\n                \"<span class=sf-dump-key>Hero Section</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>en</span>\" => []\n                </samp>]\n                \"<span class=sf-dump-key>Benefits Section</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>en</span>\" => []\n                </samp>]\n                \"<span class=sf-dump-key>How To Buy Section</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>en</span>\" => []\n                </samp>]\n                \"<span class=sf-dump-key>White logo</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>en</span>\" => []\n                </samp>]\n                \"<span class=sf-dump-key>validation</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>en</span>\" => <span class=sf-dump-note>array:105</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>accepted</span>\" => \"<span class=sf-dump-str title=\"32 characters\">The :attribute must be accepted.</span>\"\n                    \"<span class=sf-dump-key>accepted_if</span>\" => \"<span class=sf-dump-str title=\"54 characters\">The :attribute must be accepted when :other is :value.</span>\"\n                    \"<span class=sf-dump-key>active_url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">The :attribute is not a valid URL.</span>\"\n                    \"<span class=sf-dump-key>after</span>\" => \"<span class=sf-dump-str title=\"42 characters\">The :attribute must be a date after :date.</span>\"\n                    \"<span class=sf-dump-key>after_or_equal</span>\" => \"<span class=sf-dump-str title=\"54 characters\">The :attribute must be a date after or equal to :date.</span>\"\n                    \"<span class=sf-dump-key>alpha</span>\" => \"<span class=sf-dump-str title=\"41 characters\">The :attribute must only contain letters.</span>\"\n                    \"<span class=sf-dump-key>alpha_dash</span>\" => \"<span class=sf-dump-str title=\"74 characters\">The :attribute must only contain letters, numbers, dashes and underscores.</span>\"\n                    \"<span class=sf-dump-key>alpha_num</span>\" => \"<span class=sf-dump-str title=\"53 characters\">The :attribute must only contain letters and numbers.</span>\"\n                    \"<span class=sf-dump-key>array</span>\" => \"<span class=sf-dump-str title=\"32 characters\">The :attribute must be an array.</span>\"\n                    \"<span class=sf-dump-key>ascii</span>\" => \"<span class=sf-dump-str title=\"87 characters\">The :attribute field must only contain single-byte alphanumeric characters and symbols.</span>\"\n                    \"<span class=sf-dump-key>before</span>\" => \"<span class=sf-dump-str title=\"43 characters\">The :attribute must be a date before :date.</span>\"\n                    \"<span class=sf-dump-key>before_or_equal</span>\" => \"<span class=sf-dump-str title=\"55 characters\">The :attribute must be a date before or equal to :date.</span>\"\n                    \"<span class=sf-dump-key>between</span>\" => <span class=sf-dump-note>array:4</span> [ &#8230;4]\n                    \"<span class=sf-dump-key>boolean</span>\" => \"<span class=sf-dump-str title=\"43 characters\">The :attribute field must be true or false.</span>\"\n                    \"<span class=sf-dump-key>can</span>\" => \"<span class=sf-dump-str title=\"52 characters\">The :attribute field contains an unauthorized value.</span>\"\n                    \"<span class=sf-dump-key>confirmed</span>\" => \"<span class=sf-dump-str title=\"43 characters\">The :attribute confirmation does not match.</span>\"\n                    \"<span class=sf-dump-key>contains</span>\" => \"<span class=sf-dump-str title=\"49 characters\">The :attribute field is missing a required value.</span>\"\n                    \"<span class=sf-dump-key>current_password</span>\" => \"<span class=sf-dump-str title=\"26 characters\">The password is incorrect.</span>\"\n                    \"<span class=sf-dump-key>date</span>\" => \"<span class=sf-dump-str title=\"35 characters\">The :attribute is not a valid date.</span>\"\n                    \"<span class=sf-dump-key>date_equals</span>\" => \"<span class=sf-dump-str title=\"45 characters\">The :attribute must be a date equal to :date.</span>\"\n                    \"<span class=sf-dump-key>date_format</span>\" => \"<span class=sf-dump-str title=\"49 characters\">The :attribute does not match the format :format.</span>\"\n                    \"<span class=sf-dump-key>decimal</span>\" => \"<span class=sf-dump-str title=\"55 characters\">The :attribute field must have :decimal decimal places.</span>\"\n                    \"<span class=sf-dump-key>declined</span>\" => \"<span class=sf-dump-str title=\"32 characters\">The :attribute must be declined.</span>\"\n                    \"<span class=sf-dump-key>declined_if</span>\" => \"<span class=sf-dump-str title=\"54 characters\">The :attribute must be declined when :other is :value.</span>\"\n                    \"<span class=sf-dump-key>different</span>\" => \"<span class=sf-dump-str title=\"44 characters\">The :attribute and :other must be different.</span>\"\n                    \"<span class=sf-dump-key>digits</span>\" => \"<span class=sf-dump-str title=\"38 characters\">The :attribute must be :digits digits.</span>\"\n                    \"<span class=sf-dump-key>digits_between</span>\" => \"<span class=sf-dump-str title=\"52 characters\">The :attribute must be between :min and :max digits.</span>\"\n                    \"<span class=sf-dump-key>dimensions</span>\" => \"<span class=sf-dump-str title=\"44 characters\">The :attribute has invalid image dimensions.</span>\"\n                    \"<span class=sf-dump-key>distinct</span>\" => \"<span class=sf-dump-str title=\"43 characters\">The :attribute field has a duplicate value.</span>\"\n                    \"<span class=sf-dump-key>doesnt_end_with</span>\" => \"<span class=sf-dump-str title=\"69 characters\">The :attribute field must not end with one of the following: :values.</span>\"\n                    \"<span class=sf-dump-key>doesnt_start_with</span>\" => \"<span class=sf-dump-str title=\"64 characters\">The :attribute may not start with one of the following: :values.</span>\"\n                    \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"45 characters\">The :attribute must be a valid email address.</span>\"\n                    \"<span class=sf-dump-key>ends_with</span>\" => \"<span class=sf-dump-str title=\"59 characters\">The :attribute must end with one of the following: :values.</span>\"\n                    \"<span class=sf-dump-key>enum</span>\" => \"<span class=sf-dump-str title=\"35 characters\">The selected :attribute is invalid.</span>\"\n                    \"<span class=sf-dump-key>exists</span>\" => \"<span class=sf-dump-str title=\"35 characters\">The selected :attribute is invalid.</span>\"\n                    \"<span class=sf-dump-key>extensions</span>\" => \"<span class=sf-dump-str title=\"72 characters\">The :attribute field must have one of the following extensions: :values.</span>\"\n                    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"30 characters\">The :attribute must be a file.</span>\"\n                    \"<span class=sf-dump-key>filled</span>\" => \"<span class=sf-dump-str title=\"39 characters\">The :attribute field must have a value.</span>\"\n                    \"<span class=sf-dump-key>gt</span>\" => <span class=sf-dump-note>array:4</span> [ &#8230;4]\n                    \"<span class=sf-dump-key>gte</span>\" => <span class=sf-dump-note>array:4</span> [ &#8230;4]\n                    \"<span class=sf-dump-key>hex_color</span>\" => \"<span class=sf-dump-str title=\"55 characters\">The :attribute field must be a valid hexadecimal color.</span>\"\n                    \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"32 characters\">The :attribute must be an image.</span>\"\n                    \"<span class=sf-dump-key>in</span>\" => \"<span class=sf-dump-str title=\"35 characters\">The selected :attribute is invalid.</span>\"\n                    \"<span class=sf-dump-key>in_array</span>\" => \"<span class=sf-dump-str title=\"46 characters\">The :attribute field does not exist in :other.</span>\"\n                    \"<span class=sf-dump-key>integer</span>\" => \"<span class=sf-dump-str title=\"34 characters\">The :attribute must be an integer.</span>\"\n                    \"<span class=sf-dump-key>ip</span>\" => \"<span class=sf-dump-str title=\"42 characters\">The :attribute must be a valid IP address.</span>\"\n                    \"<span class=sf-dump-key>ipv4</span>\" => \"<span class=sf-dump-str title=\"44 characters\">The :attribute must be a valid IPv4 address.</span>\"\n                    \"<span class=sf-dump-key>ipv6</span>\" => \"<span class=sf-dump-str title=\"44 characters\">The :attribute must be a valid IPv6 address.</span>\"\n                    \"<span class=sf-dump-key>json</span>\" => \"<span class=sf-dump-str title=\"43 characters\">The :attribute must be a valid JSON string.</span>\"\n                    \"<span class=sf-dump-key>list</span>\" => \"<span class=sf-dump-str title=\"36 characters\">The :attribute field must be a list.</span>\"\n                    \"<span class=sf-dump-key>lowercase</span>\" => \"<span class=sf-dump-str title=\"39 characters\">The :attribute field must be lowercase.</span>\"\n                    \"<span class=sf-dump-key>lt</span>\" => <span class=sf-dump-note>array:4</span> [ &#8230;4]\n                    \"<span class=sf-dump-key>lte</span>\" => <span class=sf-dump-note>array:4</span> [ &#8230;4]\n                    \"<span class=sf-dump-key>mac_address</span>\" => \"<span class=sf-dump-str title=\"43 characters\">The :attribute must be a valid MAC address.</span>\"\n                    \"<span class=sf-dump-key>max</span>\" => <span class=sf-dump-note>array:4</span> [ &#8230;4]\n                    \"<span class=sf-dump-key>max_digits</span>\" => \"<span class=sf-dump-str title=\"57 characters\">The :attribute field must not have more than :max digits.</span>\"\n                    \"<span class=sf-dump-key>mimes</span>\" => \"<span class=sf-dump-str title=\"47 characters\">The :attribute must be a file of type: :values.</span>\"\n                    \"<span class=sf-dump-key>mimetypes</span>\" => \"<span class=sf-dump-str title=\"47 characters\">The :attribute must be a file of type: :values.</span>\"\n                    \"<span class=sf-dump-key>min</span>\" => <span class=sf-dump-note>array:4</span> [ &#8230;4]\n                    \"<span class=sf-dump-key>min_digits</span>\" => \"<span class=sf-dump-str title=\"52 characters\">The :attribute field must have at least :min digits.</span>\"\n                    \"<span class=sf-dump-key>missing</span>\" => \"<span class=sf-dump-str title=\"37 characters\">The :attribute field must be missing.</span>\"\n                    \"<span class=sf-dump-key>missing_if</span>\" => \"<span class=sf-dump-str title=\"59 characters\">The :attribute field must be missing when :other is :value.</span>\"\n                    \"<span class=sf-dump-key>missing_unless</span>\" => \"<span class=sf-dump-str title=\"61 characters\">The :attribute field must be missing unless :other is :value.</span>\"\n                    \"<span class=sf-dump-key>missing_with</span>\" => \"<span class=sf-dump-str title=\"61 characters\">The :attribute field must be missing when :values is present.</span>\"\n                    \"<span class=sf-dump-key>missing_with_all</span>\" => \"<span class=sf-dump-str title=\"62 characters\">The :attribute field must be missing when :values are present.</span>\"\n                    \"<span class=sf-dump-key>multiple_of</span>\" => \"<span class=sf-dump-str title=\"44 characters\">The :attribute must be a multiple of :value.</span>\"\n                    \"<span class=sf-dump-key>not_in</span>\" => \"<span class=sf-dump-str title=\"35 characters\">The selected :attribute is invalid.</span>\"\n                    \"<span class=sf-dump-key>not_regex</span>\" => \"<span class=sf-dump-str title=\"33 characters\">The :attribute format is invalid.</span>\"\n                    \"<span class=sf-dump-key>numeric</span>\" => \"<span class=sf-dump-str title=\"32 characters\">The :attribute must be a number.</span>\"\n                    \"<span class=sf-dump-key>password</span>\" => <span class=sf-dump-note>array:5</span> [ &#8230;5]\n                    \"<span class=sf-dump-key>present</span>\" => \"<span class=sf-dump-str title=\"37 characters\">The :attribute field must be present.</span>\"\n                    \"<span class=sf-dump-key>present_if</span>\" => \"<span class=sf-dump-str title=\"59 characters\">The :attribute field must be present when :other is :value.</span>\"\n                    \"<span class=sf-dump-key>present_unless</span>\" => \"<span class=sf-dump-str title=\"61 characters\">The :attribute field must be present unless :other is :value.</span>\"\n                    \"<span class=sf-dump-key>present_with</span>\" => \"<span class=sf-dump-str title=\"61 characters\">The :attribute field must be present when :values is present.</span>\"\n                    \"<span class=sf-dump-key>present_with_all</span>\" => \"<span class=sf-dump-str title=\"62 characters\">The :attribute field must be present when :values are present.</span>\"\n                    \"<span class=sf-dump-key>prohibited</span>\" => \"<span class=sf-dump-str title=\"35 characters\">The :attribute field is prohibited.</span>\"\n                    \"<span class=sf-dump-key>prohibited_if</span>\" => \"<span class=sf-dump-str title=\"57 characters\">The :attribute field is prohibited when :other is :value.</span>\"\n                    \"<span class=sf-dump-key>prohibited_if_accepted</span>\" => \"<span class=sf-dump-str title=\"59 characters\">The :attribute field is prohibited when :other is accepted.</span>\"\n                    \"<span class=sf-dump-key>prohibited_if_declined</span>\" => \"<span class=sf-dump-str title=\"59 characters\">The :attribute field is prohibited when :other is declined.</span>\"\n                    \"<span class=sf-dump-key>prohibited_unless</span>\" => \"<span class=sf-dump-str title=\"63 characters\">The :attribute field is prohibited unless :other is in :values.</span>\"\n                    \"<span class=sf-dump-key>prohibits</span>\" => \"<span class=sf-dump-str title=\"57 characters\">The :attribute field prohibits :other from being present.</span>\"\n                    \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"33 characters\">The :attribute format is invalid.</span>\"\n                    \"<span class=sf-dump-key>required</span>\" => \"<span class=sf-dump-str title=\"33 characters\">The :attribute field is required.</span>\"\n                    \"<span class=sf-dump-key>required_array_keys</span>\" => \"<span class=sf-dump-str title=\"55 characters\">The :attribute field must contain entries for: :values.</span>\"\n                    \"<span class=sf-dump-key>required_if</span>\" => \"<span class=sf-dump-str title=\"55 characters\">The :attribute field is required when :other is :value.</span>\"\n                    \"<span class=sf-dump-key>required_if_accepted</span>\" => \"<span class=sf-dump-str title=\"57 characters\">The :attribute field is required when :other is accepted.</span>\"\n                    \"<span class=sf-dump-key>required_if_declined</span>\" => \"<span class=sf-dump-str title=\"57 characters\">The :attribute field is required when :other is declined.</span>\"\n                    \"<span class=sf-dump-key>required_unless</span>\" => \"<span class=sf-dump-str title=\"61 characters\">The :attribute field is required unless :other is in :values.</span>\"\n                    \"<span class=sf-dump-key>required_with</span>\" => \"<span class=sf-dump-str title=\"57 characters\">The :attribute field is required when :values is present.</span>\"\n                    \"<span class=sf-dump-key>required_with_all</span>\" => \"<span class=sf-dump-str title=\"58 characters\">The :attribute field is required when :values are present.</span>\"\n                    \"<span class=sf-dump-key>required_without</span>\" => \"<span class=sf-dump-str title=\"61 characters\">The :attribute field is required when :values is not present.</span>\"\n                    \"<span class=sf-dump-key>required_without_all</span>\" => \"<span class=sf-dump-str title=\"66 characters\">The :attribute field is required when none of :values are present.</span>\"\n                    \"<span class=sf-dump-key>same</span>\" => \"<span class=sf-dump-str title=\"37 characters\">The :attribute and :other must match.</span>\"\n                    \"<span class=sf-dump-key>size</span>\" => <span class=sf-dump-note>array:4</span> [ &#8230;4]\n                    \"<span class=sf-dump-key>starts_with</span>\" => \"<span class=sf-dump-str title=\"61 characters\">The :attribute must start with one of the following: :values.</span>\"\n                    \"<span class=sf-dump-key>string</span>\" => \"<span class=sf-dump-str title=\"32 characters\">The :attribute must be a string.</span>\"\n                    \"<span class=sf-dump-key>timezone</span>\" => \"<span class=sf-dump-str title=\"40 characters\">The :attribute must be a valid timezone.</span>\"\n                    \"<span class=sf-dump-key>unique</span>\" => \"<span class=sf-dump-str title=\"38 characters\">The :attribute has already been taken.</span>\"\n                    \"<span class=sf-dump-key>uploaded</span>\" => \"<span class=sf-dump-str title=\"32 characters\">The :attribute failed to upload.</span>\"\n                    \"<span class=sf-dump-key>uppercase</span>\" => \"<span class=sf-dump-str title=\"39 characters\">The :attribute field must be uppercase.</span>\"\n                    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">The :attribute must be a valid URL.</span>\"\n                    \"<span class=sf-dump-key>ulid</span>\" => \"<span class=sf-dump-str title=\"42 characters\">The :attribute field must be a valid ULID.</span>\"\n                    \"<span class=sf-dump-key>uuid</span>\" => \"<span class=sf-dump-str title=\"36 characters\">The :attribute must be a valid UUID.</span>\"\n                    \"<span class=sf-dump-key>custom</span>\" => <span class=sf-dump-note>array:1</span> [ &#8230;1]\n                    \"<span class=sf-dump-key>attributes</span>\" => []\n                  </samp>]\n                </samp>]\n              </samp>]\n              \"<span class=sf-dump-key>plugins/captcha</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>captcha</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>en</span>\" => <span class=sf-dump-note>array:9</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>settings</span>\" => <span class=sf-dump-note>array:17</span> [ &#8230;17]\n                    \"<span class=sf-dump-key>numbers</span>\" => <span class=sf-dump-note>array:13</span> [ &#8230;13]\n                    \"<span class=sf-dump-key>operands</span>\" => <span class=sf-dump-note>array:4</span> [ &#8230;4]\n                    \"<span class=sf-dump-key>recaptcha_disclaimer_message_with_link</span>\" => \"<span class=sf-dump-str title=\"85 characters\">This site is protected by reCAPTCHA and the Google :privacyLink and :termsLink apply.</span>\"\n                    \"<span class=sf-dump-key>privacy_policy</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Privacy Policy</span>\"\n                    \"<span class=sf-dump-key>terms_of_service</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Terms of Service</span>\"\n                    \"<span class=sf-dump-key>admin_login_form</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Admin login form</span>\"\n                    \"<span class=sf-dump-key>admin_forgot_password_form</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Admin forgot password form</span>\"\n                    \"<span class=sf-dump-key>admin_reset_password_form</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Admin reset password form</span>\"\n                  </samp>]\n                </samp>]\n              </samp>]\n              \"<span class=sf-dump-key>packages/menu</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>menu</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>en</span>\" => <span class=sf-dump-note>array:30</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Menus</span>\"\n                    \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Manage your system menus</span>\"\n                    \"<span class=sf-dump-key>key_name</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Menu name (key: :key)</span>\"\n                    \"<span class=sf-dump-key>basic_info</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Basic information</span>\"\n                    \"<span class=sf-dump-key>add_to_menu</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Add to menu</span>\"\n                    \"<span class=sf-dump-key>custom_link</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Custom link</span>\"\n                    \"<span class=sf-dump-key>add_link</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Add link</span>\"\n                    \"<span class=sf-dump-key>structure</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Menu structure</span>\"\n                    \"<span class=sf-dump-key>remove</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Remove</span>\"\n                    \"<span class=sf-dump-key>cancel</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Cancel</span>\"\n                    \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Title</span>\"\n                    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Icon</span>\"\n                    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"3 characters\">URL</span>\"\n                    \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Target</span>\"\n                    \"<span class=sf-dump-key>css_class</span>\" => \"<span class=sf-dump-str title=\"9 characters\">CSS class</span>\"\n                    \"<span class=sf-dump-key>self_open_link</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Open link directly</span>\"\n                    \"<span class=sf-dump-key>blank_open_link</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Open link in new tab</span>\"\n                    \"<span class=sf-dump-key>create</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Create menu</span>\"\n                    \"<span class=sf-dump-key>menu_settings</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Menu settings</span>\"\n                     &#8230;11\n                  </samp>]\n                </samp>]\n              </samp>]\n              \"<span class=sf-dump-key>packages/page</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>pages</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>en</span>\" => <span class=sf-dump-note>array:12</span> [ &#8230;12]\n                </samp>]\n              </samp>]\n              \"<span class=sf-dump-key>plugins/blog</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>base</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>en</span>\" => <span class=sf-dump-note>array:14</span> [ &#8230;14]\n                </samp>]\n              </samp>]\n              \"<span class=sf-dump-key>plugins/ecommerce</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>products</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>en</span>\" => <span class=sf-dump-note>array:84</span> [ &#8230;84]\n                </samp>]\n                \"<span class=sf-dump-key>brands</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>en</span>\" => <span class=sf-dump-note>array:14</span> [ &#8230;14]\n                </samp>]\n                \"<span class=sf-dump-key>product-categories</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>en</span>\" => <span class=sf-dump-note>array:12</span> [ &#8230;12]\n                </samp>]\n                \"<span class=sf-dump-key>product-tag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>en</span>\" => <span class=sf-dump-note>array:5</span> [ &#8230;5]\n                </samp>]\n                \"<span class=sf-dump-key>product-model</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>en</span>\" => <span class=sf-dump-note>array:5</span> [ &#8230;5]\n                </samp>]\n              </samp>]\n              \"<span class=sf-dump-key>plugins/marketplace</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>store</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>en</span>\" => <span class=sf-dump-note>array:27</span> [ &#8230;27]\n                </samp>]\n                \"<span class=sf-dump-key>category</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>en</span>\" => <span class=sf-dump-note>array:2</span> [ &#8230;2]\n                </samp>]\n                \"<span class=sf-dump-key>marketplace</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>en</span>\" => <span class=sf-dump-note>array:22</span> [ &#8230;22]\n                </samp>]\n              </samp>]\n              \"<span class=sf-dump-key>packages/slug</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>slug</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>en</span>\" => <span class=sf-dump-note>array:10</span> [ &#8230;10]\n                </samp>]\n              </samp>]\n              \"<span class=sf-dump-key>packages/widget</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>widget</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>en</span>\" => <span class=sf-dump-note>array:29</span> [ &#8230;29]\n                </samp>]\n              </samp>]\n              \"<span class=sf-dump-key>plugins/contact</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>contact</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>en</span>\" => <span class=sf-dump-note>array:44</span> [ &#8230;44]\n                </samp>]\n              </samp>]\n              \"<span class=sf-dump-key>plugins/simple-slider</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>simple-slider</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>en</span>\" => <span class=sf-dump-note>array:15</span> [ &#8230;15]\n                </samp>]\n              </samp>]\n              \"<span class=sf-dump-key>plugins/ads</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>ads</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>en</span>\" => <span class=sf-dump-note>array:19</span> [ &#8230;19]\n                </samp>]\n              </samp>]\n              \"<span class=sf-dump-key>core/base</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>base</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>en</span>\" => <span class=sf-dump-note>array:22</span> [ &#8230;22]\n                </samp>]\n              </samp>]\n              \"<span class=sf-dump-key>plugins/newsletter</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>newsletter</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>en</span>\" => <span class=sf-dump-note>array:5</span> [ &#8230;5]\n                </samp>]\n              </samp>]\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">selector</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">determineLocalesUsing</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">stringableHandlers</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">missingTranslationKeyCallback</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">handleMissingTranslationKeys</span>: <span class=sf-dump-const>true</span>\n          </samp>}\n          #<span class=sf-dump-protected title=\"Protected property\">container</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref24 title=\"4 occurrences\">#4</a> &#8230;44}\n          #<span class=sf-dump-protected title=\"Protected property\">presenceVerifier</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Validation\\DatabasePresenceVerifier\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Validation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">DatabasePresenceVerifier</span></span> {<a class=sf-dump-ref>#1628</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">db</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\DatabaseManager\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">DatabaseManager</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2187 title=\"2 occurrences\">#187</a><samp data-depth=7 id=sf-dump-**********-ref2187 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">app</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref24 title=\"4 occurrences\">#4</a> &#8230;44}\n              #<span class=sf-dump-protected title=\"Protected property\">factory</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Connectors\\ConnectionFactory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Connectors</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ConnectionFactory</span></span> {<a class=sf-dump-ref>#188</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">container</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref24 title=\"4 occurrences\">#4</a> &#8230;44}\n              </samp>}\n              #<span class=sf-dump-protected title=\"Protected property\">connections</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>mysql</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\MySqlConnection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">MySqlConnection</span></span> {<a class=sf-dump-ref>#191</a> &#8230;24}\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">dynamicConnectionConfigurations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">extensions</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">reconnector</span>: <span class=sf-dump-note>Closure($connection)</span> {<a class=sf-dump-ref>#189</a><samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-meta>class</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"Illuminate\\Database\\DatabaseManager\n35 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Database</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span><span class=\"sf-dump-ellipsis-tail\">DatabaseManager</span></span>\"\n                <span class=sf-dump-meta>this</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\DatabaseManager\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">DatabaseManager</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2187 title=\"2 occurrences\">#187</a>}\n                <span class=sf-dump-meta>file</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php\n90 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\muhrak\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Database\\DatabaseManager.php</span></span>\"\n                <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"8 characters\">79 to 83</span>\"\n              </samp>}\n            </samp>}\n            #<span class=sf-dump-protected title=\"Protected property\">connection</span>: <span class=sf-dump-const>null</span>\n          </samp>}\n          #<span class=sf-dump-protected title=\"Protected property\">failedRules</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>Exists</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">ec_customers</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              </samp>]\n            </samp>]\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">excludeAttributes</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">messages</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\MessageBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">MessageBag</span></span> {<a class=sf-dump-ref>#4361</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">messages</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">The selected customer id is invalid.</span>\"\n              </samp>]\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">format</span>: \"<span class=sf-dump-str title=\"8 characters\">:message</span>\"\n          </samp>}\n          #<span class=sf-dump-protected title=\"Protected property\">data</span>: <span class=sf-dump-note>array:29</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FtY4EDLj9P6dViTp4GrWt6OFO4QWGtrBz9X4LzQ3</span>\"\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">MENTPACK</span>\"\n            \"<span class=sf-dump-key>model</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Botble\\Marketplace\\Models\\Store</span>\"\n            \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"8 characters\">mentpack</span>\"\n            \"<span class=sf-dump-key>slug_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n            \"<span class=sf-dump-key>is_slug_editable</span>\" => \"<span class=sf-dump-str>1</span>\"\n            \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"21 characters\"><EMAIL>.347</span>\"\n            \"<span class=sf-dump-key>phone</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"178 characters\">Welcome to Mentpack, a global player in the packaging industry. Founded in 1996, we have over 25 years of experience in providing innovative and high-quality packaging solutions.</span>\"\n            \"<span class=sf-dump-key>content</span>\" => \"\"\"\n              <span class=sf-dump-str title=\"298 characters\">&lt;p&gt;We offer a wide range of products, including Stick Pack, Sachet Machines, and Vertical Form Fill Seal packaging machines. Our ability to cater to both entry-level and worldwide brands has positioned us as a preferred solution partner for packaging challenges.&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n              <span class=sf-dump-str title=\"298 characters\"><span class=\"sf-dump-default\">\\t</span><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n              <span class=sf-dump-str title=\"298 characters\"><span class=\"sf-dump-default\">\\t</span>&lt;p&gt;<span class=sf-dump-default>\\u{A0}</span>&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n              <span class=sf-dump-str title=\"298 characters\"><span class=\"sf-dump-default\">\\t</span><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n              <span class=sf-dump-str title=\"298 characters\"><span class=\"sf-dump-default\">\\t</span>&lt;/undefined&gt;</span>\n              \"\"\"\n            \"<span class=sf-dump-key>country</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>state</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>city</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>address</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>company</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>tax_id</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>social_links</span>\" => <span class=sf-dump-note>array:15</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>facebook</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>twitter</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>instagram</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>pinterest</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>youtube</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>linkedin</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>messenger</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>flickr</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>tiktok</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>skype</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>snapchat</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>tumblr</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>whatsapp</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>wechat</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>vimeo</span>\" => <span class=sf-dump-const>null</span>\n            </samp>]\n            \"<span class=sf-dump-key>botble-marketplace-tables-data-room-table_length</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n            \"<span class=sf-dump-key>distributors</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>seo_meta</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>seo_title</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>seo_description</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>index</span>\" => \"<span class=sf-dump-str title=\"5 characters\">index</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>seo_meta_image</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"5 characters\">en_US</span>\"\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">published</span>\"\n            \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n            \"<span class=sf-dump-key>logo</span>\" => \"<span class=sf-dump-str title=\"37 characters\">lkt-alshash-2025-06-13-fy-53447-m.png</span>\"\n            \"<span class=sf-dump-key>logo_square</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>cover_image</span>\" => \"<span class=sf-dump-str title=\"17 characters\">mentpack-team.jpg</span>\"\n            \"<span class=sf-dump-key>_js_validation</span>\" => \"<span class=sf-dump-str title=\"11 characters\">customer_id</span>\"\n            \"<span class=sf-dump-key>_js_validation_validate_all</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">initialRules</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-key>2</span> => \"<span class=sf-dump-str title=\"22 characters\">exists:ec_customers,id</span>\"\n            </samp>]\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">rules</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">exists:ec_customers,id</span>\"\n            </samp>]\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">currentRule</span>: \"<span class=sf-dump-str title=\"22 characters\">exists:ec_customers,id</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">implicitAttributes</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">implicitAttributesFormatter</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">distinctValues</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">after</span>: []\n          +<span class=sf-dump-public title=\"Public property\">customMessages</span>: []\n          +<span class=sf-dump-public title=\"Public property\">fallbackMessages</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>captcha</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Captcha Verification Failed!</span>\"\n            \"<span class=sf-dump-key>math_captcha</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Math Captcha Verification Failed!</span>\"\n          </samp>]\n          +<span class=sf-dump-public title=\"Public property\">customAttributes</span>: []\n          +<span class=sf-dump-public title=\"Public property\">customValues</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">stopOnFirstFailure</span>: <span class=sf-dump-const>false</span>\n          +<span class=sf-dump-public title=\"Public property\">excludeUnvalidatedArrayKeys</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">extensions</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>no_js_validation</span>\" => <span class=sf-dump-note>Closure()</span> {<a class=sf-dump-ref>#1619</a><samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-meta>class</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"Botble\\JsValidation\\Providers\\JsValidationServiceProvider\n57 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Botble\\JsValidation\\Providers</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span><span class=\"sf-dump-ellipsis-tail\">JsValidationServiceProvider</span></span>\"\n              <span class=sf-dump-meta>this</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\JsValidation\\Providers\\JsValidationServiceProvider\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\JsValidation\\Providers</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">JsValidationServiceProvider</span></span> {<a class=sf-dump-ref>#220</a> &#8230;}\n              <span class=sf-dump-meta>file</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"D:\\laragon\\www\\muhrak\\platform\\core\\js-validation\\src\\Providers\\JsValidationServiceProvider.php\n95 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\muhrak\\platform\\core</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\</span><span class=\"sf-dump-ellipsis-tail\">js-validation\\src\\Providers\\JsValidationServiceProvider.php</span></span>\"\n              <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"8 characters\">33 to 35</span>\"\n            </samp>}\n            \"<span class=sf-dump-key>captcha</span>\" => <span class=sf-dump-note>Closure($attribute, $value, $parameters)</span> {<a class=sf-dump-ref>#2332</a><samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-meta>class</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"Botble\\Captcha\\Providers\\CaptchaServiceProvider\n47 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Botble\\Captcha\\Providers</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span><span class=\"sf-dump-ellipsis-tail\">CaptchaServiceProvider</span></span>\"\n              <span class=sf-dump-meta>this</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Captcha\\Providers\\CaptchaServiceProvider\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Captcha\\Providers</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CaptchaServiceProvider</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21662 title=\"2 occurrences\">#1662</a> &#8230;}\n              <span class=sf-dump-meta>use</span>: {<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-meta>$app</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref24 title=\"4 occurrences\">#4</a> &#8230;44}\n              </samp>}\n              <span class=sf-dump-meta>file</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"D:\\laragon\\www\\muhrak\\platform\\plugins\\captcha\\src\\Providers\\CaptchaServiceProvider.php\n87 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\muhrak</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\</span><span class=\"sf-dump-ellipsis-tail\">platform\\plugins\\captcha\\src\\Providers\\CaptchaServiceProvider.php</span></span>\"\n              <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">188 to 206</span>\"\n            </samp>}\n            \"<span class=sf-dump-key>math_captcha</span>\" => <span class=sf-dump-note>Closure($attribute, $value)</span> {<a class=sf-dump-ref>#2333</a><samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-meta>class</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"Botble\\Captcha\\Providers\\CaptchaServiceProvider\n47 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Botble\\Captcha\\Providers</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span><span class=\"sf-dump-ellipsis-tail\">CaptchaServiceProvider</span></span>\"\n              <span class=sf-dump-meta>this</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Captcha\\Providers\\CaptchaServiceProvider\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Captcha\\Providers</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CaptchaServiceProvider</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21662 title=\"2 occurrences\">#1662</a> &#8230;}\n              <span class=sf-dump-meta>file</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"D:\\laragon\\www\\muhrak\\platform\\plugins\\captcha\\src\\Providers\\CaptchaServiceProvider.php\n87 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\muhrak</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\</span><span class=\"sf-dump-ellipsis-tail\">platform\\plugins\\captcha\\src\\Providers\\CaptchaServiceProvider.php</span></span>\"\n              <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">208 to 214</span>\"\n            </samp>}\n            \"<span class=sf-dump-key>js_validation</span>\" => <span class=sf-dump-note>Closure($attribute, $value, $parameters, Validator $validator)</span> {<a class=sf-dump-ref>#3890</a><samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-meta>class</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"Botble\\JsValidation\\Remote\\Resolver\n35 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Botble\\JsValidation\\Remote</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span><span class=\"sf-dump-ellipsis-tail\">Resolver</span></span>\"\n              <span class=sf-dump-meta>this</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\JsValidation\\Remote\\Resolver\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\JsValidation\\Remote</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Resolver</span></span> {<a class=sf-dump-ref>#3891</a> &#8230;}\n              <span class=sf-dump-meta>file</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"D:\\laragon\\www\\muhrak\\platform\\core\\js-validation\\src\\Remote\\Resolver.php\n73 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\muhrak\\platform\\core</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\</span><span class=\"sf-dump-ellipsis-tail\">js-validation\\src\\Remote\\Resolver.php</span></span>\"\n              <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"8 characters\">81 to 86</span>\"\n            </samp>}\n          </samp>]\n          +<span class=sf-dump-public title=\"Public property\">replacers</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">fileRules</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">Between</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"10 characters\">Dimensions</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">Extensions</span>\"\n            <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"4 characters\">File</span>\"\n            <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"5 characters\">Image</span>\"\n            <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"3 characters\">Max</span>\"\n            <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"5 characters\">Mimes</span>\"\n            <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"9 characters\">Mimetypes</span>\"\n            <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"3 characters\">Min</span>\"\n            <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"4 characters\">Size</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">implicitRules</span>: <span class=sf-dump-note>array:24</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">Accepted</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"10 characters\">AcceptedIf</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">Declined</span>\"\n            <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"10 characters\">DeclinedIf</span>\"\n            <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"6 characters\">Filled</span>\"\n            <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"7 characters\">Missing</span>\"\n            <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"9 characters\">MissingIf</span>\"\n            <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"13 characters\">MissingUnless</span>\"\n            <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"11 characters\">MissingWith</span>\"\n            <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"14 characters\">MissingWithAll</span>\"\n            <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"7 characters\">Present</span>\"\n            <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"9 characters\">PresentIf</span>\"\n            <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"13 characters\">PresentUnless</span>\"\n            <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"11 characters\">PresentWith</span>\"\n            <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"14 characters\">PresentWithAll</span>\"\n            <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"8 characters\">Required</span>\"\n            <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"10 characters\">RequiredIf</span>\"\n            <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"18 characters\">RequiredIfAccepted</span>\"\n            <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"18 characters\">RequiredIfDeclined</span>\"\n            <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"14 characters\">RequiredUnless</span>\"\n            <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"12 characters\">RequiredWith</span>\"\n            <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"15 characters\">RequiredWithAll</span>\"\n            <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"15 characters\">RequiredWithout</span>\"\n            <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"18 characters\">RequiredWithoutAll</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">dependentRules</span>: <span class=sf-dump-note>array:40</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">After</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"12 characters\">AfterOrEqual</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"6 characters\">Before</span>\"\n            <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"13 characters\">BeforeOrEqual</span>\"\n            <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"9 characters\">Confirmed</span>\"\n            <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"9 characters\">Different</span>\"\n            <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"9 characters\">ExcludeIf</span>\"\n            <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"13 characters\">ExcludeUnless</span>\"\n            <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"11 characters\">ExcludeWith</span>\"\n            <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"14 characters\">ExcludeWithout</span>\"\n            <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"2 characters\">Gt</span>\"\n            <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"3 characters\">Gte</span>\"\n            <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"2 characters\">Lt</span>\"\n            <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"3 characters\">Lte</span>\"\n            <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"10 characters\">AcceptedIf</span>\"\n            <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"10 characters\">DeclinedIf</span>\"\n            <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"10 characters\">RequiredIf</span>\"\n            <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"18 characters\">RequiredIfAccepted</span>\"\n            <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"18 characters\">RequiredIfDeclined</span>\"\n            <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"14 characters\">RequiredUnless</span>\"\n            <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"12 characters\">RequiredWith</span>\"\n            <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"15 characters\">RequiredWithAll</span>\"\n            <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"15 characters\">RequiredWithout</span>\"\n            <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"18 characters\">RequiredWithoutAll</span>\"\n            <span class=sf-dump-index>24</span> => \"<span class=sf-dump-str title=\"9 characters\">PresentIf</span>\"\n            <span class=sf-dump-index>25</span> => \"<span class=sf-dump-str title=\"13 characters\">PresentUnless</span>\"\n            <span class=sf-dump-index>26</span> => \"<span class=sf-dump-str title=\"11 characters\">PresentWith</span>\"\n            <span class=sf-dump-index>27</span> => \"<span class=sf-dump-str title=\"14 characters\">PresentWithAll</span>\"\n            <span class=sf-dump-index>28</span> => \"<span class=sf-dump-str title=\"10 characters\">Prohibited</span>\"\n            <span class=sf-dump-index>29</span> => \"<span class=sf-dump-str title=\"12 characters\">ProhibitedIf</span>\"\n            <span class=sf-dump-index>30</span> => \"<span class=sf-dump-str title=\"20 characters\">ProhibitedIfAccepted</span>\"\n            <span class=sf-dump-index>31</span> => \"<span class=sf-dump-str title=\"20 characters\">ProhibitedIfDeclined</span>\"\n            <span class=sf-dump-index>32</span> => \"<span class=sf-dump-str title=\"16 characters\">ProhibitedUnless</span>\"\n            <span class=sf-dump-index>33</span> => \"<span class=sf-dump-str title=\"9 characters\">Prohibits</span>\"\n            <span class=sf-dump-index>34</span> => \"<span class=sf-dump-str title=\"9 characters\">MissingIf</span>\"\n            <span class=sf-dump-index>35</span> => \"<span class=sf-dump-str title=\"13 characters\">MissingUnless</span>\"\n            <span class=sf-dump-index>36</span> => \"<span class=sf-dump-str title=\"11 characters\">MissingWith</span>\"\n            <span class=sf-dump-index>37</span> => \"<span class=sf-dump-str title=\"14 characters\">MissingWithAll</span>\"\n            <span class=sf-dump-index>38</span> => \"<span class=sf-dump-str title=\"4 characters\">Same</span>\"\n            <span class=sf-dump-index>39</span> => \"<span class=sf-dump-str title=\"6 characters\">Unique</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">excludeRules</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">Exclude</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"9 characters\">ExcludeIf</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"13 characters\">ExcludeUnless</span>\"\n            <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"11 characters\">ExcludeWith</span>\"\n            <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"14 characters\">ExcludeWithout</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">sizeRules</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">Size</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"7 characters\">Between</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"3 characters\">Min</span>\"\n            <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"3 characters\">Max</span>\"\n            <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"2 characters\">Gt</span>\"\n            <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"2 characters\">Lt</span>\"\n            <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"3 characters\">Gte</span>\"\n            <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"3 characters\">Lte</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">numericRules</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">Numeric</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"7 characters\">Integer</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"7 characters\">Decimal</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">defaultNumericRules</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">Numeric</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"7 characters\">Integer</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"7 characters\">Decimal</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">exception</span>: \"<span class=sf-dump-str title=\"41 characters\">Illuminate\\Validation\\ValidationException</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">ensureExponentWithinAllowedRangeUsing</span>: <span class=sf-dump-const>null</span>\n        </samp>}\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"64 characters\">vendor/laravel/framework/src/Illuminate/Validation/Validator.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>686</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__call</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Illuminate\\Validation\\Validator</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">validateJsValidation</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">_js_validation</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"11 characters\">customer_id</span>\"\n        <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n        </samp>]\n        <span class=sf-dump-index>3</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Validation\\Validator\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Validation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Validator</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref24358 title=\"2 occurrences\">#4358</a>}\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"64 characters\">vendor/laravel/framework/src/Illuminate/Validation/Validator.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>481</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"17 characters\">validateAttribute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Illuminate\\Validation\\Validator</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">_js_validation</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"12 characters\">JsValidation</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"64 characters\">vendor/laravel/framework/src/Illuminate/Validation/Validator.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>516</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">passes</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Illuminate\\Validation\\Validator</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"81 characters\">vendor/laravel/framework/src/Illuminate/Validation/ValidatesWhenResolvedTrait.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"5 characters\">fails</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Illuminate\\Validation\\Validator</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"91 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Providers/FormRequestServiceProvider.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>30</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"16 characters\">validateResolved</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Illuminate\\Foundation\\Http\\FormRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Container/Container.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1432</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Illuminate\\Foundation\\Providers\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"58 characters\">Illuminate\\Foundation\\Providers\\FormRequestServiceProvider</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"54 characters\">[object Botble\\Marketplace\\Http\\Requests\\StoreRequest]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"42 characters\">[object Illuminate\\Foundation\\Application]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Container/Container.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1368</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"17 characters\">fireCallbackArray</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Container\\Container</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"54 characters\">[object Botble\\Marketplace\\Http\\Requests\\StoreRequest]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>Closure($resolved)</span> {<a class=sf-dump-ref>#377</a><samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-meta>class</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Providers\\FormRequestServiceProvider\n58 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Foundation\\Providers</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span><span class=\"sf-dump-ellipsis-tail\">FormRequestServiceProvider</span></span>\"\n          <span class=sf-dump-meta>this</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Providers\\FormRequestServiceProvider\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation\\Providers</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">FormRequestServiceProvider</span></span> {<a class=sf-dump-ref>#97</a> &#8230;}\n          <span class=sf-dump-meta>file</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers\\FormRequestServiceProvider.php\n113 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\muhrak\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Foundation\\Providers\\FormRequestServiceProvider.php</span></span>\"\n          <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"8 characters\">29 to 31</span>\"\n        </samp>}\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Container/Container.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1354</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"27 characters\">fireAfterResolvingCallbacks</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Container\\Container</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"45 characters\">Botble\\Marketplace\\Http\\Requests\\StoreRequest</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"54 characters\">[object Botble\\Marketplace\\Http\\Requests\\StoreRequest]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Container/Container.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>850</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"22 characters\">fireResolvingCallbacks</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Container\\Container</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"45 characters\">Botble\\Marketplace\\Http\\Requests\\StoreRequest</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"54 characters\">[object Botble\\Marketplace\\Http\\Requests\\StoreRequest]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Application.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1078</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"7 characters\">resolve</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Container\\Container</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"45 characters\">Botble\\Marketplace\\Http\\Requests\\StoreRequest</span>\"\n      <span class=sf-dump-index>1</span> => []\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-const>true</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Container/Container.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>763</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"7 characters\">resolve</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Application</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"45 characters\">Botble\\Marketplace\\Http\\Requests\\StoreRequest</span>\"\n      <span class=sf-dump-index>1</span> => []\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Application.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1058</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">make</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Container\\Container</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"45 characters\">Botble\\Marketplace\\Http\\Requests\\StoreRequest</span>\"\n      <span class=sf-dump-index>1</span> => []\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"77 characters\">vendor/laravel/framework/src/Illuminate/Routing/ResolvesRouteDependencies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>92</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">make</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Application</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"45 characters\">Botble\\Marketplace\\Http\\Requests\\StoreRequest</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"77 characters\">vendor/laravel/framework/src/Illuminate/Routing/ResolvesRouteDependencies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">transformDependency</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Routing\\ControllerDispatcher</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">[object ReflectionParameter]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>store</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Marketplace\\Models\\Store\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Marketplace\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Store</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref24338 title=\"3 occurrences\">#4338</a><samp data-depth=5 id=sf-dump-**********-ref24338 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">mp_stores</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n          +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n          #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n          +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n          +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:24</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>412</span>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">MENTPACK</span>\"\n            \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"21 characters\"><EMAIL>.347</span>\"\n            \"<span class=sf-dump-key>phone</span>\" => \"\"\n            \"<span class=sf-dump-key>address</span>\" => \"\"\n            \"<span class=sf-dump-key>country</span>\" => \"\"\n            \"<span class=sf-dump-key>state</span>\" => \"\"\n            \"<span class=sf-dump-key>city</span>\" => \"\"\n            \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>logo</span>\" => \"<span class=sf-dump-str title=\"37 characters\">lkt-alshash-2025-06-13-fy-53447-m.png</span>\"\n            \"<span class=sf-dump-key>logo_square</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>cover_image</span>\" => \"<span class=sf-dump-str title=\"17 characters\">mentpack-team.jpg</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"178 characters\">Welcome to Mentpack, a global player in the packaging industry. Founded in 1996, we have over 25 years of experience in providing innovative and high-quality packaging solutions.</span>\"\n            \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"274 characters\">&lt;p&gt;We offer a wide range of products, including Stick Pack, Sachet Machines, and Vertical Form Fill Seal packaging machines. Our ability to cater to both entry-level and worldwide brands has positioned us as a preferred solution partner for packaging challenges.&lt;/p&gt;&lt;p&gt;<span class=sf-dump-default>\\u{A0}</span>&lt;/p&gt;</span>\"\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">published</span>\"\n            \"<span class=sf-dump-key>vendor_verified_at</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 17:17:01</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 17:17:01</span>\"\n            \"<span class=sf-dump-key>zip_code</span>\" => \"\"\n            \"<span class=sf-dump-key>company</span>\" => \"\"\n            \"<span class=sf-dump-key>tax_id</span>\" => \"\"\n            \"<span class=sf-dump-key>certificate_file</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>government_id_file</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>main_type</span>\" => <span class=sf-dump-const>null</span>\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:24</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>412</span>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">MENTPACK</span>\"\n            \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"21 characters\"><EMAIL>.347</span>\"\n            \"<span class=sf-dump-key>phone</span>\" => \"\"\n            \"<span class=sf-dump-key>address</span>\" => \"\"\n            \"<span class=sf-dump-key>country</span>\" => \"\"\n            \"<span class=sf-dump-key>state</span>\" => \"\"\n            \"<span class=sf-dump-key>city</span>\" => \"\"\n            \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>logo</span>\" => \"<span class=sf-dump-str title=\"37 characters\">lkt-alshash-2025-06-13-fy-53447-m.png</span>\"\n            \"<span class=sf-dump-key>logo_square</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>cover_image</span>\" => \"<span class=sf-dump-str title=\"17 characters\">mentpack-team.jpg</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"178 characters\">Welcome to Mentpack, a global player in the packaging industry. Founded in 1996, we have over 25 years of experience in providing innovative and high-quality packaging solutions.</span>\"\n            \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"274 characters\">&lt;p&gt;We offer a wide range of products, including Stick Pack, Sachet Machines, and Vertical Form Fill Seal packaging machines. Our ability to cater to both entry-level and worldwide brands has positioned us as a preferred solution partner for packaging challenges.&lt;/p&gt;&lt;p&gt;<span class=sf-dump-default>\\u{A0}</span>&lt;/p&gt;</span>\"\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">published</span>\"\n            \"<span class=sf-dump-key>vendor_verified_at</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 17:17:01</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 17:17:01</span>\"\n            \"<span class=sf-dump-key>zip_code</span>\" => \"\"\n            \"<span class=sf-dump-key>company</span>\" => \"\"\n            \"<span class=sf-dump-key>tax_id</span>\" => \"\"\n            \"<span class=sf-dump-key>certificate_file</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>government_id_file</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>main_type</span>\" => <span class=sf-dump-const>null</span>\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Botble\\Marketplace\\Enums\\StoreStatusEnum</span>\"\n            \"<span class=sf-dump-key>main_type</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Botble\\Ecommerce\\Enums\\MainTypeEnum</span>\"\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Botble\\Base\\Casts\\SafeContent</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Botble\\Base\\Casts\\SafeContent</span>\"\n            \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Botble\\Base\\Casts\\SafeContent</span>\"\n            \"<span class=sf-dump-key>address</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Botble\\Base\\Casts\\SafeContent</span>\"\n            \"<span class=sf-dump-key>company</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Botble\\Base\\Casts\\SafeContent</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n          +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:20</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"5 characters\">email</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"5 characters\">phone</span>\"\n            <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"7 characters\">address</span>\"\n            <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"7 characters\">country</span>\"\n            <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"5 characters\">state</span>\"\n            <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"4 characters\">city</span>\"\n            <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"11 characters\">customer_id</span>\"\n            <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"4 characters\">logo</span>\"\n            <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"11 characters\">logo_square</span>\"\n            <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"11 characters\">cover_image</span>\"\n            <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"\n            <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"7 characters\">content</span>\"\n            <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n            <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"7 characters\">company</span>\"\n            <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"8 characters\">zip_code</span>\"\n            <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"16 characters\">certificate_file</span>\"\n            <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"18 characters\">government_id_file</span>\"\n            <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"6 characters\">tax_id</span>\"\n            <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"9 characters\">main_type</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n          </samp>]\n        </samp>}\n      </samp>]\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"17 characters\">[object stdClass]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"77 characters\">vendor/laravel/framework/src/Illuminate/Routing/ResolvesRouteDependencies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>30</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"25 characters\">resolveMethodDependencies</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Routing\\ControllerDispatcher</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>store</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Marketplace\\Models\\Store\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Marketplace\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Store</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref24338 title=\"3 occurrences\">#4338</a>}\n      </samp>]\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"25 characters\">[object ReflectionMethod]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>60</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"30 characters\">resolveClassMethodDependencies</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Routing\\ControllerDispatcher</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>store</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Marketplace\\Models\\Store\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Marketplace\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Store</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref24338 title=\"3 occurrences\">#4338</a>}\n      </samp>]\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"60 characters\">[object Botble\\Marketplace\\Http\\Controllers\\StoreController]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>41</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"17 characters\">resolveParameters</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Routing\\ControllerDispatcher</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"60 characters\">[object Botble\\Marketplace\\Http\\Controllers\\StoreController]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Routing/Route.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>266</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Routing\\ControllerDispatcher</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"60 characters\">[object Botble\\Marketplace\\Http\\Controllers\\StoreController]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Routing/Route.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>212</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"13 characters\">runController</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Routing\\Route</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>808</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">run</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Routing\\Route</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>170</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Routing\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">platform/core/base/src/Http/Middleware/CoreMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>17</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>170</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Botble\\Base\\Http\\Middleware\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Botble\\Base\\Http\\Middleware\\CoreMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>127</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">platform/core/base/src/Http/Middleware/CoreMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>16</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Botble\\Base\\Http\\Middleware\\CoreMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"74 characters\">platform/plugins/ecommerce/src/Http/Middleware/CaptureCouponMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>14</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"56 characters\">Botble\\Ecommerce\\Http\\Middleware\\CaptureCouponMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"78 characters\">platform/plugins/ecommerce/src/Http/Middleware/CaptureFootprintsMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>23</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"60 characters\">Botble\\Ecommerce\\Http\\Middleware\\CaptureFootprintsMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">platform/core/base/src/Http/Middleware/HttpsProtocolMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>16</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"51 characters\">Botble\\Base\\Http\\Middleware\\HttpsProtocolMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"64 characters\">platform/core/base/src/Http/Middleware/AdminLocaleMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>33</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Botble\\Base\\Http\\Middleware\\AdminLocaleMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"59 characters\">platform/core/base/src/Http/Middleware/LocaleMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>23</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"44 characters\">Botble\\Base\\Http\\Middleware\\LocaleMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"84 characters\">platform/packages/installer/src/Http/Middleware/RedirectIfNotInstalledMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>17</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Botble\\Installer\\Http\\Middleware\\RedirectIfNotInstalledMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"81 characters\">vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Illuminate\\Routing\\Middleware\\SubstituteBindings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>46</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"54 characters\">platform/core/acl/src/Http/Middleware/Authenticate.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>37</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>47</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Botble\\ACL\\Http\\Middleware\\Authenticate</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>48</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"86 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>88</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>49</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>50</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>51</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\View\\Middleware\\ShareErrorsFromSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>52</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>121</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>53</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>64</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"21 characters\">handleStatefulRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Session\\Store]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>54</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>55</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>37</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>56</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>57</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>75</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>58</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Cookie\\Middleware\\EncryptCookies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>59</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>127</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>60</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>807</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>61</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>786</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">runRouteWithinStack</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>62</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>750</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">runRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>63</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>739</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dispatchToRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>64</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>201</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>65</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>170</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Foundation\\Http\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>66</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"62 characters\">platform/core/js-validation/src/RemoteValidationMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>43</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>67</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"46 characters\">Botble\\JsValidation\\RemoteValidationMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>68</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>69</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>70</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>71</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>72</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>73</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>74</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>75</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>76</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>77</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>78</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>110</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>79</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>80</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>81</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>82</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>58</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>83</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>84</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"94 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>22</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>85</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"61 characters\">Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>86</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>127</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>87</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>176</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>88</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>145</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>89</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"16 characters\">public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>23</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["        $response = new JsonResponse($result, 200);\n", "\n", "        if ($result !== true && class_exists(ValidationException::class)) {\n", "            throw new ValidationException($validator, $response);\n", "        }\n", "\n", "        throw new HttpResponseException($response);\n"], "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fjs-validation%2Fsrc%2FRemote%2FValidator.php&line=140", "ajax": false, "filename": "Validator.php", "line": "140"}}]}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.3.17", "Environment": "localhost", "Debug Mode": "Enabled", "URL": "muhrak.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 4, "nb_statements": 4, "nb_visible_statements": 4, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00236, "accumulated_duration_str": "2.36ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}], "start": **********.308395, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0, "width_percent": 16.949}, {"sql": "select * from `mp_stores` where `id` = '412' limit 1", "type": "query", "params": [], "bindings": ["412"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 61}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 961}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 42}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.31197, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 16.949, "width_percent": 14.407}, {"sql": "select `lang_locale`, `lang_code`, `lang_name`, `lang_flag`, `lang_is_rtl` from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\Providers\\HookServiceProvider.php", "line": 105}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "platform/core/base/src/Http/Middleware/AdminLocaleMiddleware.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php", "line": 28}], "start": **********.316837, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 31.356, "width_percent": 12.712}, {"sql": "select count(*) as aggregate from `ec_customers` where `id` = '0'", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 984}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 955}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 686}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 481}], "start": **********.340634, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "muhrak", "explain": null, "start_percent": 44.068, "width_percent": 55.932}]}, "models": {"data": {"Botble\\Language\\Models\\Language": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "Botble\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Botble\\Marketplace\\Models\\Store": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FModels%2FStore.php&line=1", "ajax": false, "filename": "Store.php", "line": "?"}}}, "count": 4, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://muhrak.gc/admin/marketplaces/stores/edit/412", "action_name": "marketplace.store.edit.update", "controller_action": "Botble\\Marketplace\\Http\\Controllers\\StoreController@update", "uri": "POST admin/marketplaces/stores/edit/{store}", "controller": "Botble\\Marketplace\\Http\\Controllers\\StoreController@update<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FHttp%2FControllers%2FStoreController.php&line=116\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Botble\\Marketplace\\Http\\Controllers", "prefix": "admin/marketplaces/stores", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FHttp%2FControllers%2FStoreController.php&line=116\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/plugins/marketplace/src/Http/Controllers/StoreController.php:116-138</a>", "middleware": "web, core, auth", "duration": "923ms", "peak_memory": "52MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1201914757 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1201914757\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1557563211 data-indent-pad=\"  \"><span class=sf-dump-note>array:29</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FtY4EDLj9P6dViTp4GrWt6OFO4QWGtrBz9X4LzQ3</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">MENTPACK</span>\"\n  \"<span class=sf-dump-key>model</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Botble\\Marketplace\\Models\\Store</span>\"\n  \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"8 characters\">mentpack</span>\"\n  \"<span class=sf-dump-key>slug_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>is_slug_editable</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"21 characters\"><EMAIL>.347</span>\"\n  \"<span class=sf-dump-key>phone</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"178 characters\">Welcome to Mentpack, a global player in the packaging industry. Founded in 1996, we have over 25 years of experience in providing innovative and high-quality packaging solutions.</span>\"\n  \"<span class=sf-dump-key>content</span>\" => \"\"\"\n    <span class=sf-dump-str title=\"298 characters\">&lt;p&gt;We offer a wide range of products, including Stick Pack, Sachet Machines, and Vertical Form Fill Seal packaging machines. Our ability to cater to both entry-level and worldwide brands has positioned us as a preferred solution partner for packaging challenges.&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"298 characters\"><span class=\"sf-dump-default\">\\t</span><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"298 characters\"><span class=\"sf-dump-default\">\\t</span>&lt;p&gt;<span class=sf-dump-default>\\u{A0}</span>&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"298 characters\"><span class=\"sf-dump-default\">\\t</span><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"298 characters\"><span class=\"sf-dump-default\">\\t</span>&lt;/undefined&gt;</span>\n    \"\"\"\n  \"<span class=sf-dump-key>country</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>state</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>city</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>address</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>company</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>tax_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>social_links</span>\" => <span class=sf-dump-note>array:15</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>facebook</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>twitter</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>instagram</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>pinterest</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>youtube</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>linkedin</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>messenger</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>flickr</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>tiktok</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>skype</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>snapchat</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>tumblr</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>whatsapp</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>wechat</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>vimeo</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>botble-marketplace-tables-data-room-table_length</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>distributors</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>seo_meta</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>seo_title</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>seo_description</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>index</span>\" => \"<span class=sf-dump-str title=\"5 characters\">index</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>seo_meta_image</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"5 characters\">en_US</span>\"\n  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">published</span>\"\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>logo</span>\" => \"<span class=sf-dump-str title=\"37 characters\">lkt-alshash-2025-06-13-fy-53447-m.png</span>\"\n  \"<span class=sf-dump-key>logo_square</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cover_image</span>\" => \"<span class=sf-dump-str title=\"17 characters\">mentpack-team.jpg</span>\"\n  \"<span class=sf-dump-key>_js_validation</span>\" => \"<span class=sf-dump-str title=\"11 characters\">customer_id</span>\"\n  \"<span class=sf-dump-key>_js_validation_validate_all</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1557563211\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1779176008 data-indent-pad=\"  \"><span class=sf-dump-note>array:21</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"288 characters\">eyJpdiI6Im1JMlNVTWtVbkRiOEg0SWVQcGVNYnc9PSIsInZhbHVlIjoiZFl5K2xnVGtwR2dRMHhqMEd2ZU5UYnJ6MHRIOUM5Mys4cnFCZGs5NDhFZ3JkK3ZWU0ViYi9mZ29NNG05a2dMN05mY2tCNzRRWElyZmxFcDYzdWRxelE9PSIsIm1hYyI6ImM5YzIxZDQ1YzFhMDQ2YzVjOWIxNTdlNDhjYzY2NDUwY2QyNjA1Y2RmNDE5N2YwNmIwNmNjOTQ5MDcyMzliOGQiLCJ0YWciOiIifQ==</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">muhrak.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1592</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">FtY4EDLj9P6dViTp4GrWt6OFO4QWGtrBz9X4LzQ3</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">https://muhrak.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">https://muhrak.gc/admin/marketplaces/stores/edit/412</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3140 characters\">cookie_for_consent=1; botble_footprints_cookie=eyJpdiI6InJtenpwcG01ZldyRCs2TFV4YUYrYUE9PSIsInZhbHVlIjoiamVKQ2MwT2VxdDI3L0pXTXdlMURsY0JJSHdaWVp5T1VoSUNSbEpIZ0pZcHdKWGZId3lBLzJ2b1pIUXVYUUxKRjdMY3k5Q1VpTWRFd3Ftbzd6L3UyeHUvdWFpdTAwUXBNZER4MDc5MGJjOXQxQXhIazNiaTI0TU42YXJkbHNVek8iLCJtYWMiOiI3YWM1Njc0ODBhMzFhMjBlOWM2NzQ2YTE1ODRkNGM0ODk5YTNhYjE5ZjliNjY3NGI2MzA5MjMxNGJhNDliZjJjIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6ImozUjJ6OXN5RkhxbXdCYmc5K1JURlE9PSIsInZhbHVlIjoib3plTVp0NnFEZVhoU1NQejZBMklhY2RqSEtwTmNvaG1SeWtpbFkvZmVZb21QUmg4VTFvRVlPbUUzd1NGQ3J6dFZVT0hmdDIrTWd0SGF1Y28yYWx1VWJYdTJ0OUFvdkxJM3U3UktZNXpBL1pReU8xcDI0S3AxSmRkVzVHaHhyZHlia1ptb0wrTUhkNHRPbXhoY2pNL2JoZ2JXVGNTaHI2VjRIRW1HOUdiVHAwN2xxdkJLazdwZDFadCtJaTNlTk9HT2JmWms2Vnp5bGorWkxFd1dvaStjUHlEY1ZQR2tYOW5tREUxV25Vek1jbDFkenJlZENXR3VIaHM4aFdLaDRVNjViY21idXByU3ZQQVFCU2gxcVNnYW5zbERUamgxZjNUMVRIWEt4V0tUbGxFRDc5VzhDNkZRZ0g1QXRjU3Rna3ZTQlRwZjVScFM1eEdzUmNhSlAxaHpIZ2kxdVEvOG51K1FlVmhuSTBGdUZxYXdkdGdWLzFDdW1nZ0MvdDFnTit6STdZd3JJWnVCNGZZSTNFRWtjaTJndzZVT3prNEkrb1c0cTFFaGFWckxiUUtVSFVsYXlWSWxnUSttR2dxYlo0K0dCY1NzRDBGdzVNZGdLTnFOZG43VllRTkFORnIrZlk1SEt3ZFkvNjYwdjJFWmFJaCtPUVh4TjFZejFleUQvU09oeERvVkdyVVJ2d0ZkM25TZXU1OGNZOE5PWTJWVmV4WWpsWUE2Zmc0dHV3PSIsIm1hYyI6IjYwMTc0ODhiZDNjN2VkZGVmZjliNmYyNWU0ZTU2ZWE5YjhkMjllNzk1ZDAxNGU2ZWU3MDhkOTA3NzM3OTFjNjUiLCJ0YWciOiIifQ%3D%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IktsRlR4UnBrM0szS1Y0SUxrVVlUdHc9PSIsInZhbHVlIjoibVgzWjBpSHR4T2o4NlJBc3ZWNEVGZGVXS0xzU3NlNjRPOXFyNVFIOVVkbCt1ODRxbVZFNkNaODlSa3BORzF3bTJEb3Rkb0RVTVVjZHNBVFZiVnQ2TlhLOVBNVkJhVUIzb0ZZV2VTOER2dEJjd1ZZZEw3OWg4UEV4OXZqSjQwR2hsbERhOTdCQ1puTTJGRG1nMnozejdpcXp0NllsWUVzYWl5SjFlZGhYVWdUNmYyY2xRVXdXeG9UU3M3S1kxMnVxbVc5bktlTHFEOHNJbDlUV2JPamxrK3AwangreXlUMmtBTVhRdlN0SHQwcz0iLCJtYWMiOiIzNTliMzJjOTQxODc2NDdkNDI0YTBiNjZkMzUzZjEzNzZjYWMwYzlmNDQ2NGZhZjJiOTk5M2NhYTc0YmEyOGY5IiwidGFnIjoiIn0%3D; remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ijl6MUxxZXpoME96cm9sVGNGeUlyaEE9PSIsInZhbHVlIjoiWlR1dnBBRnpBZWF2c1I3dndid2M5UllhSUVPN0Fqa1p0K0RGelpiVkRpditYckx0YWZ2RVZFbTMwUGVnVGh4ODc5VFBseUtFaytWNWpRalB1QVhJbHZONFhqaFc2UjdjTlI1SlR0TDAwMS9ldmZxN1F5aTZpaWdnaFNOaGs0VmRWMlBoNkhMeTZKSU1hVTA3UHRKNUJKb3hPUEJVUWtEcjc2RFZmQ3NVanJEeHNHMU1jc3QzQ2N5blJKUUJVaFNsUEFQSmVKSHBiTjhtVmU2OEUwY1dBMTNyblVPOENzcTlRcHBOOTVuTWc1OD0iLCJtYWMiOiJhNzJhNzEyNGRjOTM0ZmVhMTIyNDU0ZmM0Mzc2MDQzZjI3MjNjNmM4NTUwMGQ2ZTZlNmZjM2NlMWZkYTgyMGM0IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InhwbU9Hd0FqTW1EdlZzcmllQi9SaEE9PSIsInZhbHVlIjoiZ3l3Ull1dnNZNThCeW5MU3F2MjczcmtVOUx3VVA3OFYwRUV6alRoL0wrRExibjU2YVlvaXpubWhGVFE3OEVINWxHUlNhcjlod3NRVkREMG9MbWRoL29wa1hraFRQUUhlMVNHK3I4NExSbEt4U1VkZkw0KytBeUVZcHdXVjZocDMiLCJtYWMiOiI4ZTE5NWU4YTE1ZjYwODAxMGEzMzUxZTcyOGM3Y2Y0OGEzZDViMmIwMDQ5MDhmNTgyODY0OWM1Zjc2YjY1OWVmIiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6IjFlQWZpSDV1OVFhSGVMbkkxS1l5R3c9PSIsInZhbHVlIjoiUUFUUUlDTUpXdlpNY1BWaUNsYXZIZS9hZEg1OHhzMTZOU3krT1R0RXhFazkyMjFlLy9OVmo0UGhnL3k3bTlRSnMxd1BYT01mb2lRN1VQWXVvS2R5L1ZqdG1zbVBlTVVtd1F6RDdJd05OWi93aUpMN0Q2eS9QSjRPNWc4MTF1MGUiLCJtYWMiOiIzYzJlMWMyYjQ4ZTg5YWZlYTlkOGVlNzE2YmQwM2VlZTczYjI1YTVlN2RkMjBiOWNjMzE5NjhiOWVkYmU3MGI0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1779176008\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">14908b951300852119d2c46221b4b11a7e2b8107</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"359 characters\">{&quot;footprint&quot;:&quot;14908b951300852119d2c46221b4b11a7e2b8107&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;muhrak.gc&quot;,&quot;landing_page&quot;:&quot;install\\/welcome&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|lEoydfVrZKIFerelBjYotnW4P2e0TuoqJSFCOOr89XMprC6ygbibdEjl3rvX|$2y$12$6oEMzkNhsgbeo4WDg72G9e2nWxuvMvozEUdkIQ0AfpRz3PJNQ7XzG</span>\"\n  \"<span class=sf-dump-key>remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">2|eZ0awTe8bfQLceQTos5gvZ7R55kW3IiLBR997mfYDDJcPndE2TcWxwpqf39P|$2y$12$EU5h35Jju9igRCRES6rqCuE42l30uwJ94rSesc56rboRI0xQqYy/G</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FtY4EDLj9P6dViTp4GrWt6OFO4QWGtrBz9X4LzQ3</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7jtlU2dXcBjwVHs9Eymy9yeCbICkdDlwWf2uJ6r5</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 13 Jun 2025 17:18:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1628244926 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FtY4EDLj9P6dViTp4GrWt6OFO4QWGtrBz9X4LzQ3</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"43 characters\">https://muhrak.gc/admin/marketplaces/stores</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1628244926\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://muhrak.gc/admin/marketplaces/stores/edit/412", "action_name": "marketplace.store.edit.update", "controller_action": "Botble\\Marketplace\\Http\\Controllers\\StoreController@update"}, "badge": null}}